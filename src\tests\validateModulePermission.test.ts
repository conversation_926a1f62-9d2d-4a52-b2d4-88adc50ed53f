/**
 * Test cases for validateModulePermission function
 * Tests the permission validation using MORole, MOPermission, and MOModule models
 */

import { validateModulePermission } from '../helper/common';
import { User, user_status } from '../models/User';
import { Role as MORole } from '../models/MORole';
import { MOPermission } from '../models/MOPermission';
import { MOModule } from '../models/MOModule';
import { ROLE_PERMISSIONS } from '../helper/constant';

// Mock the models
jest.mock('../models/User');
jest.mock('../models/MORole');
jest.mock('../models/MOPermission');
jest.mock('../models/MOModule');

const mockUser = User as jest.Mocked<typeof User>;
const mockMORole = MORole as jest.Mocked<typeof MORole>;
const mockMOPermission = MOPermission as jest.Mocked<typeof MOPermission>;
const mockMOModule = MOModule as jest.Mocked<typeof MOModule>;

describe('validateModulePermission', () => {
  // Test data
  const testUser = {
    id: 1,
    organization_id: 'org-123',
    user_active_role_id: 2,
    web_user_active_role_id: 3,
    user_status: user_status.ACTIVE
  };

  const testRole = {
    id: 3,
    role_name: 'Manager',
    role_status: 'active',
    organization_id: 'org-123'
  };

  const testModule = {
    id: 2,
    module: 'staff',
    module_name: 'Staff Management'
  };

  const testPermission = {
    id: 1,
    role_id: 3,
    module_id: 2, // staff module ID is 2 according to mo_modules.sql
    permission: 7, // VIEW + CREATE + EDIT (1 + 2 + 4)
    status: 'active',
    organization_id: 'org-123'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Suppress console.log for cleaner test output
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Successful permission validation', () => {
    test('should return true when user has VIEW permission', async () => {
      // Setup mocks
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        permission: ROLE_PERMISSIONS.VIEW
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(true);
    });

    test('should return true when user has CREATE permission', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        permission: ROLE_PERMISSIONS.CREATE
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.CREATE
      );

      expect(result).toBe(true);
    });

    test('should return true when user has EDIT permission', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        permission: ROLE_PERMISSIONS.EDIT
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.EDIT
      );

      expect(result).toBe(true);
    });

    test('should return true when user has DELETE permission', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        permission: ROLE_PERMISSIONS.DELETE
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.DELETE
      );

      expect(result).toBe(true);
    });

    test('should return true when user has combined permissions', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        permission: ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT // 6
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT
      );

      expect(result).toBe(true);
    });

    test('should work with user ID instead of user object', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        permission: ROLE_PERMISSIONS.VIEW
      } as any);

      const result = await validateModulePermission(
        1, // user ID instead of object
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(true);
    });
  });

  describe('Failed permission validation - Missing parameters', () => {
    test('should return false when user is null/undefined', async () => {
      const result = await validateModulePermission(
        null,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when organization_id is missing', async () => {
      const result = await validateModulePermission(
        testUser,
        '',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when module_slug is missing', async () => {
      const result = await validateModulePermission(
        testUser,
        'org-123',
        '', // Invalid module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when permission_type is undefined', async () => {
      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        undefined as any
      );

      expect(result).toBe(false);
    });
  });

  describe('Failed permission validation - User not found/invalid', () => {
    test('should return false when user not found', async () => {
      mockUser.findOne.mockResolvedValue(null);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when user is in different organization', async () => {
      mockUser.findOne.mockResolvedValue(null); // User not found in org

      const result = await validateModulePermission(
        testUser,
        'different-org',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when user status is PENDING', async () => {
      mockUser.findOne.mockResolvedValue(null); // Filtered out by user_status

      const result = await validateModulePermission(
        { ...testUser, user_status: user_status.PENDING },
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when user has no active role', async () => {
      mockUser.findOne.mockResolvedValue({
        ...testUser,
        user_active_role_id: null,
        web_user_active_role_id: null
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });
  });

  describe('Failed permission validation - Role issues', () => {
    test('should return false when role not found', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(null);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when role is inactive', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(null); // Role filtered out by status

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when role is in different organization', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(null); // Role filtered out by org

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });
  });

  describe('Failed permission validation - Module issues', () => {
    test('should return false when module not found', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(null);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'non_existent_module', // Non-existent module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });
  });

  describe('Failed permission validation - Permission issues', () => {
    test('should return false when no permission record found', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue(null);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when user lacks required permission', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        permission: ROLE_PERMISSIONS.VIEW // User only has VIEW
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.CREATE // But needs CREATE
      );

      expect(result).toBe(false);
    });

    test('should return false when permission status is inactive', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      mockMOPermission.findOne.mockResolvedValue(null); // Filtered out by status

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });
  });

  describe('Error handling', () => {
    test('should return false when database error occurs', async () => {
      mockUser.findOne.mockRejectedValue(new Error('Database connection failed'));

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });

    test('should return false when role query fails', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockRejectedValue(new Error('Role query failed'));

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(false);
    });
  });

  describe('Bitwise permission checks', () => {
    test('should correctly validate bitwise permissions', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue(testModule as any);
      
      // User has VIEW + CREATE + EDIT (1 + 2 + 4 = 7)
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        permission: 7
      } as any);

      // Should have VIEW
      expect(await validateModulePermission(testUser, 'org-123', 'staff', ROLE_PERMISSIONS.VIEW)).toBe(true);

      // Should have CREATE
      expect(await validateModulePermission(testUser, 'org-123', 'staff', ROLE_PERMISSIONS.CREATE)).toBe(true);

      // Should have EDIT
      expect(await validateModulePermission(testUser, 'org-123', 'staff', ROLE_PERMISSIONS.EDIT)).toBe(true);

      // Should NOT have DELETE
      expect(await validateModulePermission(testUser, 'org-123', 'staff', ROLE_PERMISSIONS.DELETE)).toBe(false);
    });
  });

  describe('Module slug mapping verification', () => {
    test('should work with dashboard module', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue({
        id: 1,
        module: 'dashboard',
        module_name: 'Dashboard'
      } as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        module_id: 1,
        permission: ROLE_PERMISSIONS.VIEW
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'dashboard', // dashboard module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(true);
    });

    test('should work with branch module', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue({
        id: 3,
        module: 'branch',
        module_name: 'Branch Management'
      } as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        module_id: 3,
        permission: ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'branch', // branch module slug
        ROLE_PERMISSIONS.CREATE
      );

      expect(result).toBe(true);
    });

    test('should work with staff module', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue({
        id: 7,
        module: 'staff',
        module_name: 'Staff Management'
      } as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        module_id: 7,
        permission: ROLE_PERMISSIONS.VIEW | ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT | ROLE_PERMISSIONS.DELETE
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'staff', // staff module slug
        ROLE_PERMISSIONS.DELETE
      );

      expect(result).toBe(true);
    });

    test('should work with company_setting module', async () => {
      mockUser.findOne.mockResolvedValue(testUser as any);
      mockMORole.findOne.mockResolvedValue(testRole as any);
      mockMOModule.findOne.mockResolvedValue({
        id: 27,
        module: 'company_setting',
        module_name: 'Company Setting'
      } as any);
      mockMOPermission.findOne.mockResolvedValue({
        ...testPermission,
        module_id: 27,
        permission: ROLE_PERMISSIONS.VIEW
      } as any);

      const result = await validateModulePermission(
        testUser,
        'org-123',
        'company_setting', // company_setting module slug
        ROLE_PERMISSIONS.VIEW
      );

      expect(result).toBe(true);
    });
  });
});
