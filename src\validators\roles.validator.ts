import { Segments, Jo<PERSON>, celebrate } from "celebrate";
import { widget_type, widget_sub_type, widget_category } from "../models/MOWidget";
export default {
    createRole: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_name: Joi.string().required(),
                platform: Joi.number().valid(1, 2, 3).required().messages({
                    'any.only': 'Platform must be 1 (Web), 2 (Mobile), or 3 (Both)',
                    'any.required': 'Platform is required'
                }), // 1=web, 2=mobile, 3=both
                parent_role_id: Joi.number().required().messages({
                    'number.base': 'Parent role ID must be a number',
                    'number.integer': 'Parent role ID must be an integer',
                    'number.positive': 'Parent role ID must be positive',
                    'any.required': 'Parent role ID is required'
                }),
                additional_permissions: Joi.object().allow(null), // Role-level additional permissions
            }),
        }),
    updateRole: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_name: Joi.string().allow(null, ""),
                platform: Joi.number().valid(1, 2, 3).allow(null), // 1=web, 2=mobile, 3=both
                parent_role_id: Joi.number().allow(null),
                additional_permissions: Joi.object().allow(null), // Role-level additional permissions
                widget_categories: Joi.array().items(
                    Joi.string().valid(...Object.values(widget_category))
                ).optional().messages({
                    'array.base': 'Widget categories must be an array',
                    'any.only': `Widget category must be one of: ${Object.values(widget_category).join(', ')}`
                })
            }),
        }),
    createPermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_id: Joi.number().required(),
                module_ids: Joi.array().items(Joi.number()).required(),
                partial: Joi.boolean().required(),
                permission: Joi.number().required(),
                platform: Joi.number().valid(1, 2, 3).required(), // 1=web, 2=mobile, 3=both
                additional_permissions: Joi.object().allow(null), // Module-level additional permissions
            }),
        }),
    updatePermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                roles: Joi.array().items( // Array of roles with their permissions
                    Joi.object().keys({
                        role_id: Joi.number().required(),
                        modules: Joi.array().items( // Array of modules with permissions
                            Joi.object().keys({
                                module_id: Joi.number().required(),
                                permission: Joi.number().required(), // Permission value (0-15)
                                partial: Joi.boolean().allow(null)
                            })
                        ).required()
                    })
                ).required(),
            }),
        }),
    copyPermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                from_role: Joi.number().required(),
                to_role: Joi.array().items(Joi.number()).min(1).required(), // Array of role IDs
            }),
        }),
    updateWidgetPermission: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                role_id: Joi.number().required(),
                widgets: Joi.array().items(
                    Joi.object().keys({
                        widget_id: Joi.number().required(),
                        permission: Joi.number().min(0).max(15).required(), // Permission value (0-15)
                        order: Joi.number().min(0).optional(), // Optional order field
                    })
                ).min(1).required(), // At least one widget is required
            }),
        }),
    deleteWidgetPermission: () =>
        celebrate({
            [Segments.PARAMS]: Joi.object().keys({
                id: Joi.number().required(), // role_id
            }),
        }),
    copyWidgetPermissions: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                from_role: Joi.number().required(),
                to_role: Joi.alternatives().try(
                    Joi.number(),
                    Joi.array().items(Joi.number()).min(1)
                ).required(), // Single role ID or array of role IDs
            }),
        }),

    // Widget validators
    createWidget: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                name: Joi.string().min(1).max(255).required().messages({
                    'string.empty': 'Widget name cannot be empty',
                    'string.min': 'Widget name must be at least 1 character long',
                    'string.max': 'Widget name cannot exceed 255 characters',
                    'any.required': 'Widget name is required'
                }),
                widget: Joi.string().required().messages({
                    'string.empty': 'Widget content cannot be empty',
                    'any.required': 'Widget content is required'
                }),
                type: Joi.string().valid(...Object.values(widget_type)).required().messages({
                    'any.only': `Widget type must be one of: ${Object.values(widget_type).join(', ')}`,
                    'any.required': 'Widget type is required'
                }),
                subType: Joi.string().valid(...Object.values(widget_sub_type)).required().messages({
                    'any.only': `Widget sub type must be one of: ${Object.values(widget_sub_type).join(', ')}`,
                    'any.required': 'Widget sub type is required'
                }),
                filters: Joi.alternatives().try(
                    Joi.array().items(Joi.object()),
                    Joi.object(),
                    Joi.string().custom((value, helpers) => {
                        try {
                            JSON.parse(value);
                            return value;
                        } catch (error) {
                            return helpers.error('string.invalidJSON');
                        }
                    }),
                    Joi.allow(null)
                ).optional().messages({
                    'alternatives.match': 'Filters must be a valid JSON array, object, string, or null',
                    'string.invalidJSON': 'Filters string must be valid JSON'
                }),
                slug: Joi.string().min(1).max(100).pattern(/^[a-z0-9_-]+$/).required().messages({
                    'string.empty': 'Widget slug cannot be empty',
                    'string.min': 'Widget slug must be at least 1 character long',
                    'string.max': 'Widget slug cannot exceed 100 characters',
                    'string.pattern.base': 'Widget slug can only contain lowercase letters, numbers, underscores, and hyphens',
                    'any.required': 'Widget slug is required'
                }),
                call_api: Joi.boolean().optional().messages({
                    'boolean.base': 'Call API must be a boolean value'
                }),
                widget_category: Joi.string().valid(...Object.values(widget_category)).optional().messages({
                    'any.only': `Widget category must be one of: ${Object.values(widget_category).join(', ')}`
                })
            }),
        }),

    updateWidget: () =>
        celebrate({
            [Segments.PARAMS]: Joi.object().keys({
                id: Joi.number().integer().positive().required().messages({
                    'number.base': 'Widget ID must be a number',
                    'number.integer': 'Widget ID must be an integer',
                    'number.positive': 'Widget ID must be positive',
                    'any.required': 'Widget ID is required'
                })
            }),
            [Segments.BODY]: Joi.object().keys({
                name: Joi.string().min(1).max(255).optional().messages({
                    'string.empty': 'Widget name cannot be empty',
                    'string.min': 'Widget name must be at least 1 character long',
                    'string.max': 'Widget name cannot exceed 255 characters'
                }),
                widget: Joi.string().optional().messages({
                    'string.empty': 'Widget content cannot be empty'
                }),
                type: Joi.string().valid(...Object.values(widget_type)).optional().messages({
                    'any.only': `Widget type must be one of: ${Object.values(widget_type).join(', ')}`
                }),
                subType: Joi.string().valid(...Object.values(widget_sub_type)).optional().messages({
                    'any.only': `Widget sub type must be one of: ${Object.values(widget_sub_type).join(', ')}`
                }),
                filters: Joi.alternatives().try(
                    Joi.array().items(Joi.object()),
                    Joi.object(),
                    Joi.string().custom((value, helpers) => {
                        try {
                            JSON.parse(value);
                            return value;
                        } catch (error) {
                            return helpers.error('string.invalidJSON');
                        }
                    }),
                    Joi.allow(null)
                ).optional().messages({
                    'alternatives.match': 'Filters must be a valid JSON array, object, string, or null',
                    'string.invalidJSON': 'Filters string must be valid JSON'
                }),
                slug: Joi.string().min(1).max(100).pattern(/^[a-z0-9_-]+$/).optional().messages({
                    'string.empty': 'Widget slug cannot be empty',
                    'string.min': 'Widget slug must be at least 1 character long',
                    'string.max': 'Widget slug cannot exceed 100 characters',
                    'string.pattern.base': 'Widget slug can only contain lowercase letters, numbers, underscores, and hyphens'
                }),
                call_api: Joi.boolean().optional().messages({
                    'boolean.base': 'Call API must be a boolean value'
                }),
                widget_category: Joi.string().valid(...Object.values(widget_category)).optional().messages({
                    'any.only': `Widget category must be one of: ${Object.values(widget_category).join(', ')}`
                })
            }).min(1).messages({
                'object.min': 'At least one field must be provided for update'
            }),
        }),

    deleteWidget: () =>
        celebrate({
            [Segments.PARAMS]: Joi.object().keys({
                id: Joi.number().integer().positive().required().messages({
                    'number.base': 'Widget ID must be a number',
                    'number.integer': 'Widget ID must be an integer',
                    'number.positive': 'Widget ID must be positive',
                    'any.required': 'Widget ID is required'
                })
            }),
        }),

    getWidgetById: () =>
        celebrate({
            [Segments.PARAMS]: Joi.object().keys({
                id: Joi.number().integer().positive().required().messages({
                    'number.base': 'Widget ID must be a number',
                    'number.integer': 'Widget ID must be an integer',
                    'number.positive': 'Widget ID must be positive',
                    'any.required': 'Widget ID is required'
                })
            }),
        }),

    updateIndexes: () =>
        celebrate({
            [Segments.QUERY]: Joi.object().keys({
                organization_id: Joi.string().optional().messages({
                    'string.base': 'Organization ID must be a string'
                })
            }),
        }),
};