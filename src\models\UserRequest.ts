"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { LeaveTypeModel } from "./LeaveType";

interface requestAttributes {
  id: number;
  request_subject: string;
  request_type: string;
  request_reason: string;
  from_user_id: number;
  leave_days: number;
  start_date: string;
  end_date: string;
  request_approved_by: number;
  approved_by_reason: string;
  request_status: string;
  role_id: number;
  leave_request_type: number;
  leave_deduction_type: string;
  leave_calculation_type: string;
  leave_period_type: string;
  leave_old_calculation: number;
  leave_days_obj: string;
  duration_type: string;
  created_by: number;
  updated_by: number;
}

export enum request_type {
  CASUAL = "casual",
  EMERGENCY = "emergency",
  ONBORDING = "onbording",
  DELETE = "delete",
  RESIGN = "resign",
}
export enum request_status {
  APPROVED = "approved",
  PENDING = "pending",
  REJECTED = "rejected",
  CANCELLED = "cancelled"
}

export enum leave_deduction_type {
  PAID = "paid",
  UNPAID = "unpaid"
}

export enum duration_type {
  DAYS = "Days",
  HOURS = "Hours"
}

export enum leave_period_type {
  DAYS = "day",
  HOURS = "hour"
}

export enum leave_calculation_type {
  AUTO = "auto",
  MANUAL = "manual"
}



export class UserRequest
  extends Model<requestAttributes, never>
  implements requestAttributes {
  id!: number;
  request_type!: string;
  request_reason!: string;
  request_subject!: string;
  from_user_id!: number;
  start_date!: string;
  end_date!: string;
  leave_days!: number;
  request_approved_by!: number;
  approved_by_reason!: string;
  request_status!: string;
  role_id!: number;
  leave_request_type!: number;
  leave_deduction_type!: string;
  leave_calculation_type!: string;
  leave_period_type!: string;
  leave_old_calculation!: number
  leave_days_obj!: string;
  duration_type!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }
}

UserRequest.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    request_type: {
      type: DataTypes.ENUM,
      values: Object.values(request_type),
    },
    from_user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    request_subject: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    request_approved_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    start_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    end_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    leave_days: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    request_reason: {
      type: DataTypes.TEXT('long'),
      allowNull: false,
    },
    approved_by_reason: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    },
    request_status: {
      type: DataTypes.ENUM,
      values: Object.values(request_status),
      defaultValue: request_status.PENDING,
    },
    duration_type: {
      type: DataTypes.ENUM,
      values: Object.values(duration_type),
      defaultValue: duration_type.DAYS,
    },
    leave_request_type: {
      type: DataTypes.INTEGER,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    leave_calculation_type: {
      type: DataTypes.ENUM,
      values: Object.values(leave_calculation_type),
    },
    leave_period_type: {
      type: DataTypes.ENUM,
      values: Object.values(leave_period_type),
      defaultValue: leave_period_type.DAYS
    },
    leave_old_calculation: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
    leave_deduction_type: {
      type: DataTypes.ENUM,
      values: Object.values(leave_deduction_type),
      allowNull: true,
    },
    leave_days_obj: {
      type: DataTypes.TEXT("long"),
      allowNull: true,
    }
  },
  {
    sequelize: sequelize,
    tableName: "nv_request",
    modelName: "UserRequest",
  },
);

// UserRequest.belongsTo(User, {
//   foreignKey: "from_user_id",
//   as: "request_from_users",
// });
// User.hasMany(UserRequest, {
//   foreignKey: "from_user_id",
//   as: "request_from_user",
// });
// UserRequest.belongsTo(User, {
//   foreignKey: "request_approved_by",
//   as: "request_approved_users",
// });
// User.hasMany(UserRequest, {
//   foreignKey: "request_approved_by",
//   as: "request_approved_user",
// });

// UserRequest.belongsTo(Role, { foreignKey: "role_id", as: "leave_role_request" });
// Role.hasOne(UserRequest, { foreignKey: "role_id", as: "leave_role" });

// Role.hasMany(UserRequest, { foreignKey: "role_id", as: "leave_role" });
// UserRequest.belongsTo(Role, { foreignKey: "role_id", as: "leave_role_request" });

LeaveTypeModel.hasOne(UserRequest, { foreignKey: "leave_request_type", as: "leave_type" });
UserRequest.belongsTo(LeaveTypeModel, { foreignKey: "leave_request_type", as: "leave_request_type_list" });

UserRequest.addHook("afterUpdate", async (userRequest: any) => {
  await addActivity("UserRequest", "updated", userRequest);
});

UserRequest.addHook("afterCreate", async (userRequest: UserRequest) => {
  await addActivity("UserRequest", "created", userRequest);
});

UserRequest.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});

