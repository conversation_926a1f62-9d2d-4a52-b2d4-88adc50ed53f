"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";
import { MOPermission } from "./MOPermission";
import { UserRequest } from "./UserRequest";

interface roleAttributes {
  id: number;
  index: number;
  role_name: string;
  role_status: string;
  parent_role_id: number;
  platform: number;
  additional_permissions: string;
  created_by: number;
  updated_by: number;
  organization_id: string;
}

/** Role enum  for status*/
export enum role_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export class Role
  extends Model<roleAttributes, never>
  implements roleAttributes
{
  id!: number;
  index!: number;
  role_name!: string;
  role_status!: string;
  parent_role_id!: number;
  platform!: number;
  additional_permissions!: string;
  created_by!: number;
  updated_by!: number;
  organization_id!: string;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers
      return data
    })

    return this;
  }

  // Auto-generate index based on organization_id
  public static async generateIndex(organization_id: string): Promise<number> {
    const lastRole = await Role.findOne({
      where: { organization_id },
      order: [['index', 'DESC']],
      attributes: ['index']
    });

    return lastRole ? lastRole.index + 1 : 1;
  }
}

Role.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    index: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    role_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    role_status: {
      type: DataTypes.ENUM,
      values: Object.values(role_status),
    },
    parent_role_id: {
      type: DataTypes.INTEGER,
    },
    platform: {
      type: DataTypes.INTEGER,
    },
    additional_permissions: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
    organization_id: {
      type: DataTypes.STRING,
    }
  },
  {
    sequelize: sequelize,
    tableName: "mo_roles",
    modelName: "MORole",
    timestamps: true,
  },
);

// Associations
Role.hasMany(MOPermission, { foreignKey: "role_id" });
MOPermission.belongsTo(Role, { foreignKey: "role_id", as: "role" });

UserRequest.belongsTo(Role, { foreignKey: "role_id", as: "leave_role_request" });
Role.hasOne(UserRequest, { foreignKey: "role_id", as: "leave_role" });

// Auto-generate index before creating a role
Role.addHook("beforeCreate", async (role: any) => {
  if (!role.index && role.organization_id) {
    role.index = await Role.generateIndex(role.organization_id);
  }
});

Role.addHook("afterUpdate", async (role: any) => {
  await addActivity("MORole", "updated", role);
});

Role.addHook("afterCreate", async (role: Role) => {
  await addActivity("MORole", "created", role);
});