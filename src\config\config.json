{"development": {"use_env_variable": false, "PORT": 8002, "OTP_EXPIRE_TIME": "300", "JWT_SECRET": "jwt_token_secret_namaste_village", "JWT_EXIPIRATION_TIME": "1d", "FROM_EMAIL": "<EMAIL>", "REFRESH_TOKEN_SECRET": "asjkfuyboqwkejwdjvq47", "REFRESH_TOKEN_EXPIRE": 1200, "RESEND_OTP_ATTEMPT": 5, "FORGOT_PASSWORD_ATTEMPT": 3, "EMAIL_HOST": "smtp.hostinger.com", "EMAIL_PORT": 465, "EMAIL_PASSWORD": "Namaste@2024", "SIGNAL_APP_ID": "9abf08f0-3527-48d2-8e52-fd6e5886245a", "SIGNAL_API_KEY": "NjhmMjhlN2MtMGEyMi00YzcwLTgwOTgtYTY1NjEzZWFhZjZl", "IS_EMAIL_USE_SMTP": "off", "ADMIN_EMAIL": "", "FRONT_BASE_URL": "", "API_BASE_URL": "http://localhost:8002/v1/private/user/get-file?location=", "SESSION_TIME": 9000, "IS_UPLOAD_AWS": false, "MAX_LEAVE": 28, "DSR_DATE_COLOR": "#000000", "DSR_TOTAL_COLOR": "#8B0000", "CATEGORY_CREATE_LIMIT": 10, "FILE_SIZE_LIMIT": 0, "VAT_PER_DATA": [0, 20], "CRON_JOB_TIME_OBJECT": {"onboardingReminder": "0 0 * * *", "removeResignedUser": "0 * * * *", "sendDsrReminderAt12AM": "2 0 * * *", "sendDsrReminderAt12PMForPreviousDay": "0 12 * * *", "sendReminderAtMondayForWsrForLastWeek": "0 12 * * 1", "sendReminderAtTuesdayForWsrForLastWeek": "0 12 * * 2", "sendReminderForLastMonthOn10Expense": "0 12 10 * *", "sendReminderForLastMonthOn25Expense": "0 12 25 * *", "budgetReminder": "0 10 * * 4", "budgetReminderTillDate": "0 10 * * *"}, "MIN_ANDROID_VERSION": "1.0.0", "MAX_ANDROID_VERSION": "1.0.0", "MIN_IOS_VERSION": "1.0.5", "MAX_IOS_VERSION": "1.0.8", "SWAGGER_BASE_URL": "localhost:8002", "RABBITMQ_URL": "amqp://admin:jnext@123@localhost:5673", "API_UPLOAD_URL": "https://immune-needlessly-porpoise.ngrok-free.app/uploads", "AUTH_API_URL": "http://localhost:8010/v1/public/auth/old-user-data", "KEYCLOAK_CLIENT_ID": "node-backend", "KEYCLOAK_SERVER_URL": "http://localhost:8080/auth/", "KEYCLOAK_REALM_NAME": "local_orga", "KEYCLOAK_SECRET_KEY": "3B74rdPL0sEEiNyRuIFpFyZSxEj10zO5", "KEYCLOAK_MY_SECRET_KEY": "keycklock-secret-key", "KEYCLOAK_BASE_URL": "http://localhost:8080/admin/realms/", "KEYCLOAK_TOKEN_BASE_URL": "http://localhost:8080/realms/", "KEYCLOAK_MASTER_ROLE": "org_master", "KEYCLOAK_MASTER_ROLE_DESCRIPTION": "role_org_master", "KEYCLOAK_SUPER_ADMIN_ROLE": "super_admin", "KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION": "role_super_admin", "KEYCLOAK_REALM_ROLE": "realm-admin", "ORGANIZATION_ID": "7bfecd3f-7eca-4a25-89b6-895c82960819", "KEYCLOAK_STAFF_ROLE": "staff", "KEYCLOAK_STAFF_ROLE_DESCRIPTION": "role_staff", "BUDGET_REMINDER_CRON": false, "MINIO_ENDPOINT": "http://************:9000", "MINIO_ACCESS_KEY": "h4SpNuJqqxju6YWPtnm9", "MINIO_SECRET_KEY": "CyTuY6asmBQNqhVP6DQyYUmPeq2kTT6ihKkmunYd"}, "staging": {"use_env_variable": false, "PORT": 9023, "OTP_EXPIRE_TIME": "300", "JWT_SECRET": "jwt_token_secret_namaste_village", "JWT_EXIPIRATION_TIME": "1d", "RESEND_OTP_ATTEMPT": 5, "FORGOT_PASSWORD_ATTEMPT": 3, "REFRESH_TOKEN_SECRET": "asjkfuyboqwkejwdjvq47", "REFRESH_TOKEN_EXPIRE": "20m", "FROM_EMAIL": "<EMAIL>", "EMAIL_HOST": "smtp.hostinger.com", "EMAIL_PORT": 465, "EMAIL_PASSWORD": "Namaste@2024", "IS_EMAIL_USE_SMTP": "on", "ADMIN_EMAIL": "", "SIGNAL_APP_ID": "72409702-e98f-446e-9fc9-6371e6b25e34", "SIGNAL_API_KEY": "NDMxYWFmMTMtMzk3YS00NmY5LThiMzgtYjYyOTE0YmEzZThl", "FRONT_BASE_URL": "https://namastevillage.theeasyaccess.com", "API_BASE_URL": "https://namastevillage.theeasyaccess.com/backend-api/v1/public/user/get-file?location=", "SESSION_TIME": "", "IS_UPLOAD_AWS": false, "MAX_LEAVE": 28, "DSR_DATE_COLOR": "#000000", "DSR_TOTAL_COLOR": "#8B0000", "FILE_SIZE_LIMIT": 0, "VAT_PER_DATA": [0, 20], "CRON_JOB_TIME_OBJECT": {"onboardingReminder": "0 0 * * *", "removeResignedUser": "0 * * * *", "sendDsrReminderAt12AM": "2 0 * * *", "sendDsrReminderAt12PMForPreviousDay": "0 12 * * *", "sendReminderAtMondayForWsrForLastWeek": "0 12 * * 1", "sendReminderAtTuesdayForWsrForLastWeek": "0 12 * * 2", "sendReminderForLastMonthOn10Expense": "0 12 10 * *", "sendReminderForLastMonthOn25Expense": "0 12 25 * *", "budgetReminder": "0 10 * * 4", "budgetReminderTillDate": "0 10 * * *"}, "MIN_ANDROID_VERSION": "1.0.0", "MAX_ANDROID_VERSION": "1.0.0", "MIN_IOS_VERSION": "1.0.5", "MAX_IOS_VERSION": "1.0.13", "SWAGGER_BASE_URL": "staging.namastevillage.theeasyaccess.com/api", "RABBITMQ_URL": "amqp://admin:jnext@123@rabbitmq:5672", "AUTH_API_URL": "http://auth-ms-service:8024/v1/public/auth/old-user-data", "BUDGET_REMINDER_CRON": true, "API_UPLOAD_URL": "https://namastevillage.theeasyaccess.com/backend-api/v1/public/user/get-file?location=", "MINIO_ENDPOINT": "http://************:9000", "MINIO_ACCESS_KEY": "h4SpNuJqqxju6YWPtnm9", "MINIO_SECRET_KEY": "CyTuY6asmBQNqhVP6DQyYUmPeq2kTT6ihKkmunYd"}, "testing": {"use_env_variable": false, "PORT": 9024, "OTP_EXPIRE_TIME": "300", "JWT_SECRET": "jwt_token_secret_namaste_village", "JWT_EXIPIRATION_TIME": "30d", "RESEND_OTP_ATTEMPT": 5, "FORGOT_PASSWORD_ATTEMPT": 3, "REFRESH_TOKEN_SECRET": "asjkfuyboqwkejwdjvq47", "REFRESH_TOKEN_EXPIRE": "20m", "FROM_EMAIL": "<EMAIL>", "SIGNAL_APP_ID": "9abf08f0-3527-48d2-8e52-fd6e5886245a", "SIGNAL_API_KEY": "NjhmMjhlN2MtMGEyMi00YzcwLTgwOTgtYTY1NjEzZWFhZjZl", "EMAIL_HOST": "smtp.hostinger.com", "EMAIL_PORT": 465, "EMAIL_PASSWORD": "Namaste@2024", "IS_EMAIL_USE_SMTP": "on", "ADMIN_EMAIL": "", "FRONT_BASE_URL": "https://namastevillage.theeasyaccess.com", "API_BASE_URL": "https://namastevillage.theeasyaccess.com/api/uploads", "SESSION_TIME": "", "IS_UPLOAD_AWS": false, "MAX_LEAVE": 28, "DSR_DATE_COLOR": "#000000", "DSR_TOTAL_COLOR": "#8B0000", "FILE_SIZE_LIMIT": 0, "VAT_PER_DATA": [0, 20], "CRON_JOB_TIME_OBJECT": {"onboardingReminder": "0 0 * * *", "removeResignedUser": "0 * * * *", "sendDsrReminderAt12AM": "2 0 * * *", "sendDsrReminderAt12PMForPreviousDay": "0 12 * * *", "sendReminderAtMondayForWsrForLastWeek": "0 12 * * 1", "sendReminderAtTuesdayForWsrForLastWeek": "0 12 * * 2", "sendReminderForLastMonthOn10Expense": "0 12 10 * *", "sendReminderForLastMonthOn25Expense": "0 12 25 * *", "budgetReminder": "0 10 * * 4", "budgetReminderTillDate": "0 10 * * *"}, "MIN_ANDROID_VERSION": "1.0.0", "MAX_ANDROID_VERSION": "1.0.0", "MIN_IOS_VERSION": "1.0.5", "MAX_IOS_VERSION": "1.0.13", "SWAGGER_BASE_URL": "namastevillage.theeasyaccess.com/api", "BUDGET_REMINDER_CRON": true}, "production": {"use_env_variable": false, "PORT": 9023, "OTP_EXPIRE_TIME": "300", "JWT_SECRET": "jwt_token_secret_namaste_village", "JWT_EXIPIRATION_TIME": "30d", "RESEND_OTP_ATTEMPT": 5, "FORGOT_PASSWORD_ATTEMPT": 3, "REFRESH_TOKEN_SECRET": "asjkfuyboqwkejwdjvq47", "REFRESH_TOKEN_EXPIRE": "20m", "FROM_EMAIL": "<EMAIL>", "SIGNAL_APP_ID": "ed9600f6-2120-45f2-91da-94b60b26a1bd", "SIGNAL_API_KEY": "NjJlZjQ1NTQtZmMyOC00YWFkLTgxN2MtZWU0OGJiYThkNGZk", "EMAIL_HOST": "smtp.hostinger.com", "EMAIL_PORT": 465, "EMAIL_PASSWORD": "Namaste@2024", "IS_EMAIL_USE_SMTP": "on", "ADMIN_EMAIL": "", "FRONT_BASE_URL": "https://portal.microffice.co.uk", "API_BASE_URL": "https://portal.microffice.co.uk/backend-api/v1/public/user/get-file?location=", "SESSION_TIME": "", "IS_UPLOAD_AWS": false, "MAX_LEAVE": 28, "DSR_DATE_COLOR": "#000000", "DSR_TOTAL_COLOR": "#8B0000", "FILE_SIZE_LIMIT": 0, "VAT_PER_DATA": [0, 20], "CRON_JOB_TIME_OBJECT": {"onboardingReminder": "0 0 * * *", "removeResignedUser": "0 * * * *", "sendDsrReminderAt12AM": "2 0 * * *", "sendDsrReminderAt12PMForPreviousDay": "0 12 * * *", "sendReminderAtMondayForWsrForLastWeek": "0 12 * * 1", "sendReminderAtTuesdayForWsrForLastWeek": "0 12 * * 2", "sendReminderForLastMonthOn10Expense": "0 12 10 * *", "sendReminderForLastMonthOn25Expense": "0 12 25 * *", "budgetReminder": "0 10 * * 4", "budgetReminderTillDate": "0 10 * * *"}, "MIN_ANDROID_VERSION": "1.0.0", "MAX_ANDROID_VERSION": "1.0.0", "MIN_IOS_VERSION": "1.0.5", "MAX_IOS_VERSION": "1.0.13", "SWAGGER_BASE_URL": "portal.microffice.co.uk/api", "RABBITMQ_URL": "amqp://JNext:JnextMO2025@rabbitmq-service:5673", "AUTH_API_URL": "http://auth-ms-service:8024/v1/public/auth/old-user-data", "BUDGET_REMINDER_CRON": true, "API_UPLOAD_URL": "https://portal.microffice.co.uk/backend-api/v1/public/user/get-file?location=", "MINIO_ENDPOINT": "http://************:9000", "MINIO_ACCESS_KEY": "h4SpNuJqqxju6YWPtnm9", "MINIO_SECRET_KEY": "CyTuY6asmBQNqhVP6DQyYUmPeq2kTT6ihKkmunYd"}}