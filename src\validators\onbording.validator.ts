import { Joi, Segments, celebrate } from "celebrate";
export default {
  createForm: Joi.object().keys({
    medical_disability: Jo<PERSON>.boolean().allow(null),
    medical_disability_detail: Joi.string().allow(null, ""),
    kin1_name: Joi.string().allow(null, ""),
    kin1_relation: Joi.string().allow(null, ""),
    kin1_address: Joi.string().allow(null, ""),
    kin1_mobile_number: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid kin1 mobile number.').allow(null, ""),
    kin2_name: Joi.string().allow(null, ""),
    kin2_relation: Joi.string().allow(null, ""),
    kin2_address: Joi.string().allow(null, ""),
    kin2_mobile_number: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid kin2 mobile number.').allow(null, ""),
    professional1_name_contact: Joi.string().allow(null, ""),
    professional1_role_description: Joi.string().allow(null, ""),
    professional2_name_contact: Joi.string().allow(null, ""),
    professional2_role_description: Joi.string().allow(null, ""),
    professional1_start_date: Joi.date().allow(null),
    professional1_end_date: Joi.date().allow(null),
    professional2_start_date: Joi.date().allow(null),
    professional2_end_date: Joi.date().allow(null),
    passport_no: Joi.string().allow(null, ""),
    issued_date: Joi.date().allow(null),
    permit_type: Joi.string().allow(null, ""),
    permit_type_other: Joi.string().allow(null, ""),
    validity: Joi.date().allow(null),
    bank_account_name: Joi.string().allow(null, ""),
    bank_account_number: Joi.string().pattern(/^[0-9]{8}$/).message('Please enter valid account number.').allow(null, ""),
    bank_sort_code: Joi.string().pattern(/^\d{2}-\d{2}-\d{2}$/).message('Please enter valid sort number.').allow(null, ""),
    bank_society_name: Joi.string().allow(null, ""),
    bank_address: Joi.string().allow(null, ""),
    insurance_number: Joi.string().allow(null, ""),
    postgraduate_loan: Joi.boolean().allow(null),
    statement_apply: Joi.boolean().allow(null),
    is_current_information: Joi.boolean().allow(null),
    another_job: Joi.boolean().allow(null),
    country: Joi.string().allow(null, ""),
    private_pension: Joi.boolean().allow(null),
    payment_from: Joi.boolean().allow(null),
    load_guidance: Joi.string().allow(null, ""),
    statementA: Joi.boolean().allow(null),
    statementB: Joi.boolean().allow(null),
    statementC: Joi.boolean().allow(null),
    healthSafetyList: Joi.array()
      .allow(null, "")
      .items(Joi.number().allow(null, ""))
      .min(1),
    is_confirm_upload: Joi.boolean().allow(null),
    is_confirm_sign: Joi.boolean().allow(null),
    health_safety_complete: Joi.boolean().allow(null),
    has_right_to_work_in_uk: Joi.boolean().allow(null),
    is_uk_citizen: Joi.boolean().allow(null),
    has_student_or_pg_loan: Joi.boolean().allow(null),
    has_p45_form: Joi.boolean().allow(null),
    cv: Joi.string().allow(null, ""),
    p45: Joi.string().allow(null, ""),
    student_letter: Joi.string().allow(null, ""),
  }),
  updateForm: Joi.object().keys({
    user_gender: Joi.string().allow(null, ""),
    user_gender_other: Joi.string().allow(null, ""),
    marital_status: Joi.string().allow(null, ""),
    marital_status_other: Joi.string().allow(null, ""),
    user_first_name: Joi.string().allow(null, ""),
    user_middle_name: Joi.string().allow(null, ""),
    user_last_name: Joi.string().allow(null, ""),
    user_phone_number: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid phone number').allow(null, ""),
    emergency_contact: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid home mobile number').allow(null, ""),
    user_email: Joi.string().allow(null, ""),
    birth_country: Joi.string().allow(null, ""),
    date_of_birth: Joi.date().allow(null),
    user_joining_date: Joi.date().allow(null),
    pin_code: Joi.string().allow(null),
    address_line1: Joi.string().allow(null, ""),
    user_designation: Joi.string().allow(null, ""),
    address_line2: Joi.string().allow(null, ""),
    medical_disability: Joi.boolean().allow(null),
    medical_disability_detail: Joi.string().allow(null, ""),
    kin1_name: Joi.string().allow(null, ""),
    kin1_relation: Joi.string().allow(null, ""),
    kin1_address: Joi.string().allow(null, ""),
    kin1_mobile_number: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid kin1 mobile number.').allow(null),
    kin2_name: Joi.string().allow(null, ""),
    kin2_relation: Joi.string().allow(null, ""),
    kin2_address: Joi.string().allow(null, ""),
    kin2_mobile_number: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid kin2 mobile number.').allow(null),
    professional1_name_contact: Joi.string().allow(null, ""),
    professional1_role_description: Joi.string().allow(null, ""),
    professional2_name_contact: Joi.string().allow(null, ""),
    professional2_role_description: Joi.string().allow(null, ""),
    professional1_start_date: Joi.date().allow(null),
    professional1_end_date: Joi.date().allow(null),
    professional2_start_date: Joi.date().allow(null),
    professional2_end_date: Joi.date().allow(null),
    passport_no: Joi.string().allow(null, ""),
    issued_date: Joi.date().allow(null),
    permit_type: Joi.string().allow(null, ""),
    permit_type_other: Joi.string().allow(null, ""),
    validity: Joi.date().allow(null),
    bank_account_name: Joi.string().allow(null, ""),
    bank_account_number: Joi.string().pattern(/^[0-9]{8}$/).message('Please enter valid account number.').allow(null, ""),
    bank_sort_code: Joi.string().pattern(/^\d{2}-\d{2}-\d{2}$/).message('Please enter valid sort number.').allow(null, ""),
    bank_society_name: Joi.string().allow(null, ""),
    bank_address: Joi.string().allow(null, ""),
    insurance_number: Joi.string().allow(null, ""),
    postgraduate_loan: Joi.boolean().allow(null),
    statement_apply: Joi.boolean().allow(null),
    is_current_information: Joi.boolean().allow(null),
    another_job: Joi.boolean().allow(null),
    country: Joi.string().allow(null, ""),
    private_pension: Joi.boolean().allow(null),
    payment_from: Joi.boolean().allow(null),
    load_guidance: Joi.string().allow(null, ""),
    statementA: Joi.boolean().allow(null),
    statementB: Joi.boolean().allow(null),
    statementC: Joi.boolean().allow(null),
    healthSafetyList: Joi.array()
      .allow(null, "")
      .items(Joi.number().allow(null, ""))
      .min(1),
    is_confirm_upload: Joi.boolean().allow(null),
    is_confirm_sign: Joi.boolean().allow(null),
    health_safety_complete: Joi.boolean().allow(null),
    has_right_to_work_in_uk: Joi.boolean().allow(null),
    is_uk_citizen: Joi.boolean().allow(null),
    has_student_or_pg_loan: Joi.boolean().allow(null),
    has_p45_form: Joi.boolean().allow(null),
    cv: Joi.string().allow(null, ""),
    p45: Joi.string().allow(null, ""),
    student_letter: Joi.string().allow(null, ""),
  }),
  formVerification: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        verifiedCheckList: Joi.array()
          .allow(null),
        to_user_id: Joi.number().integer().required(),
      }),
    }),
  UserOnboardingReset: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        checklist_ids: Joi.array().required(),
        user_id: Joi.number().integer().required(),
      }),
    }),
  deleteFile: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        field_name: Joi.array().required(),
        checklist_id: Joi.number().integer().required(),
        from_user_id: Joi.number().integer().required(),
      }),
    }),
};
