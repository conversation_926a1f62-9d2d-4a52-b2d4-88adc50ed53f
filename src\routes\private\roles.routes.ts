import { Router } from "express";
import {
    copyPermissions,
    createPermission,
    createRole,
    deletePermission,
    deleteRole,
    getPermissions,
    getRoles,
    updatePermission,
    updateRole,
    updateWidgetPermission,
    deleteWidgetPermission,
    copyWidgetPermissions,
    createModule,
    getModules,
    updateModule,
    deleteModule,
    createWidget,
    getWidgets,
    updateWidget,
    deleteWidget,
    getWidgetById,
    updateIndexes,
    migrateWidgets
} from "../../controller/roles.controller";
import rolesValidator from "../../validators/roles.validator";
import moduleValidator from "../../validators/module.validator";
const router: Router = Router();

// Role routes
router.post('/create', rolesValidator.createRole(), createRole);
router.get('/list', getRoles);
router.put('/update/:id', rolesValidator.updateRole(), updateRole);
router.delete('/delete/:id', deleteRole);

// Permission routes
router.post('/permissions', rolesValidator.createPermission(), createPermission);
router.put('/permissions', rolesValidator.updatePermission(), updatePermission);
router.put('/permissions/widget', rolesValidator.updateWidgetPermission(), updateWidgetPermission);
router.delete('/permissions/:id', deletePermission);
router.get('/permissions/:id?', getPermissions);
router.post('/permissions/copy', rolesValidator.copyPermission(), copyPermissions);

// Widget Permission routes
router.delete('/permissions/widget/:id', rolesValidator.deleteWidgetPermission(), deleteWidgetPermission);
router.post('/permissions/widget/copy', rolesValidator.copyWidgetPermissions(), copyWidgetPermissions);

// Module routes
router.post('/modules', moduleValidator.createModule(), createModule);
router.get('/modules', getModules);
router.put('/modules/:id', moduleValidator.updateModule(), updateModule);
router.delete('/modules/:id', deleteModule);

// Widget routes
router.post('/widgets', rolesValidator.createWidget(), createWidget);
router.get('/widgets', getWidgets);
router.get('/widgets/:id', rolesValidator.getWidgetById(), getWidgetById);
router.put('/widgets/:id', rolesValidator.updateWidget(), updateWidget);
router.delete('/widgets/:id', rolesValidator.deleteWidget(), deleteWidget);

// Utility routes
router.post('/update-indexes', rolesValidator.updateIndexes(), updateIndexes);

// Widget migration routes
router.post('/migrate-widgets', rolesValidator.updateIndexes(), migrateWidgets);

export default router;
