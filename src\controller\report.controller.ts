import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { getReportCategory<PERSON>ilter, getReportFilter, validateModulePermission } from "../helper/common";
import { user_filter_status, UserFilter } from "../models/UserFilter";
import { Op } from "sequelize";
import { ROLE_PERMISSIONS } from "../helper/constant";


const getFilterList = async (req: Request, res: Response) => {
    try {
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'dsr_report', // DSR Report module slug
            ROLE_PERMISSIONS.VIEW
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.DIRECTOR,
        //         ROLE_CONSTANT.HR,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //     ]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { report_filter_type }: any = req.query
        // Return the final response with the  Report filter data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: (await getReportFilter(report_filter_type)).length > 0 ? await getReportFilter(report_filter_type) : [],
        });
    } catch (error) {
        // Log any errors that occur during execution
        console.log(error);
        // Return a service unavailable status and an error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging
        });
    }
};

const getFilterCategoryList = async (req: Request, res: Response) => {
    try {
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'dsr_report', // DSR Report module slug
            ROLE_PERMISSIONS.VIEW
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [
        //         ROLE_CONSTANT.SUPER_ADMIN,
        //         ROLE_CONSTANT.ADMIN,
        //         ROLE_CONSTANT.DIRECTOR,
        //         ROLE_CONSTANT.HR,
        //         ROLE_CONSTANT.ACCOUNTANT,
        //         ROLE_CONSTANT.BRANCH_MANAGER,
        //         ROLE_CONSTANT.HOTEL_MANAGER,
        //     ]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { include_inactive, report_filter_type, branch_id } = req.query
        // Return the final response with the  Report filter category data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: (await getReportCategoryFilter(req.user.organization_id, include_inactive, report_filter_type, branch_id)).length > 0 ? await getReportCategoryFilter(req.user.organization_id, include_inactive, report_filter_type, branch_id) : [],
        });
    } catch (error) {
        // Log any errors that occur during execution
        console.log(error);
        // Return a service unavailable status and an error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging
        });
    }
};

const saveReportFilter = async (req: Request, res: Response) => {
    try {
        // Destructure required fields from the request body
        const { filter_name, filter_value, group_value, user_filter_type } = req.body;

        // Check if a filter with the same name already exists for the user
        const findFilterExist = await UserFilter.findOne({
            where: {
                filter_name, // Filter name to check
                user_id: req.user.id, // Current user's ID
                user_filter_status: { [Op.not]: user_filter_status.DELETED }, // Exclude deleted filters
                user_filter_type
            }
        });

        // If the filter already exists, return a 400 BAD REQUEST with a relevant message
        if (findFilterExist) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FILTER_ALREADY_EXISTS") // Internationalized error message
            });
        }

        // Save the new filter to the database
        const saveFilter = await UserFilter.create({
            filter_name, // Filter name from the request
            user_id: req.user.id, // Associate the filter with the current user
            filter_value, // Filter value from the request
            group_value, // Group value from the request
            user_filter_status: user_filter_status.ACTIVE, // Set filter status to active
            user_filter_type,
            created_by: req.user.id, // Track the creator
            updated_by: req.user.id // Track the updater
        } as any);

        // If the filter was successfully saved, return a success response
        if (saveFilter) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FILTER_SAVED_SUCCESSFULLY") // Internationalized success message
            });
        } else {
            // If saving the filter failed, return a 400 BAD REQUEST with a relevant message
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAILED_TO_FILTER_SAVE") // Internationalized error message
            });
        }
    } catch (error) {
        // Log any errors that occur during execution
        console.log(error);

        // Return a 503 SERVICE UNAVAILABLE status with a generic error message and error data
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic internationalized error message
            data: error // Include error details for debugging
        });
    }
};

const updateReportFilter = async (req: Request, res: Response) => {
    try {
        // Destructure necessary properties from the request body and parameters
        const { filter_name, filter_value, group_value, user_filter_type } = req.body;
        const { filter_id } = req.params;

        // Check if a filter with the same name already exists for the user
        const findFilterExist: any = await UserFilter.findOne({
            where: {
                filter_name: filter_name, // Filter name to check for duplicates
                user_id: req.user.id,    // Ensure the filter belongs to the current user
                user_filter_status: { [Op.not]: user_filter_status.DELETED }, // Exclude deleted filters
                user_filter_type
            },
        });

        // If a duplicate filter exists and it's not the current filter being updated
        if (findFilterExist && findFilterExist.id != filter_id) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FILTER_ALREADY_EXISTS"), // Inform the user that the filter name is taken
            });
        }

        // Update the filter with the provided data
        const updateFilter = await UserFilter.update(
            {
                filter_name,       // New filter name
                filter_value,      // New filter value
                group_value,       // New group value
                updated_by: req.user.id, // Track the user making the update
            },
            { where: { id: filter_id } } // Ensure only the specified filter is updated
        );

        // Check if the update was successful
        if (updateFilter.length > 0) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FILTER_UPDATED_SUCCESSFULLY"), // Success message
            });
        } else {
            // Handle cases where the update operation didn't affect any rows
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAILED_TO_FILTER_UPDATE"), // Failure message
            });
        }
    } catch (error) {
        // Log any errors that occur during execution
        console.log(error);

        // Return a service unavailable status and a generic error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging purposes
        });
    }
};

const getUserReportFilter = async (req: Request, res: Response) => {
    try {
        // Extract the filter_id from request parameters
        const { filter_id } = req.params;

        // Fetch the user filter from the database with specified attributes
        const findFilterExist: any = await UserFilter.findOne({
            attributes: ['id', 'filter_name', 'filter_value', 'group_value', 'user_filter_type', 'updatedAt'], // Select only specific fields
            where: {
                id: filter_id, // Match the filter ID
                user_filter_status: user_filter_status.ACTIVE, // Ensure the filter is active
            },
        });
        const filterObj: any = {}
        if (findFilterExist) {
            filterObj.id = findFilterExist.id
            filterObj.filter_name = findFilterExist.filter_name
            filterObj.user_filter_type = findFilterExist.user_filter_type
            filterObj.updatedAt = findFilterExist.updatedAt
            filterObj.filter_value = findFilterExist.filter_value ? filterObj.filter_value = JSON.parse(findFilterExist.filter_value) : {}
            filterObj.group_value = findFilterExist.group_value ? filterObj.group_value = JSON.parse(findFilterExist.group_value) : []
        }


        // Return the fetched filter or an empty object if not found
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"), // Success message
            data: filterObj || {}, // Send the filter data or an empty object
        });
    } catch (error) {
        // Log any errors that occur during execution
        console.log(error);

        // Return a service unavailable status and a generic error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging purposes
        });
    }
};

const getUserReportFilterList = async (req: Request, res: Response) => {
    try {
        // Extract the user_id from request parameters
        const { user_id } = req.params;
        const { user_filter_type }: any = req.query;
        // Fetch all user filters from the database with specified attributes
        const getUserFilterList = await UserFilter.findAll({
            attributes: ['id', 'filter_name', 'group_value', 'updatedAt'], // Select specific fields to return
            where: {
                user_id: user_id, // Match the user ID
                user_filter_status: { [Op.not]: [user_filter_status.DELETED] }, // Exclude deleted filters
                user_filter_type: user_filter_type
            },
            order: [['updatedAt', 'DESC']], // Sort the results by updatedAt in descending order
        });

        // Return the fetched filter list or an empty array if no filters exist
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"), // Success message
            data: getUserFilterList.length > 0 ? getUserFilterList : [], // Send the list or an empty array
        });
    } catch (error) {
        // Log any errors that occur during execution
        console.log(error);

        // Return a service unavailable status and a generic error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging purposes
        });
    }
};

const deleteUserReportFilter = async (req: Request, res: Response) => {
    try {
        // Extract the filter_id from request parameters
        const { filter_id } = req.params;

        // Check if the filter exists and is active
        const findFilterExist = await UserFilter.findOne({
            where: {
                id: filter_id, // Match the filter ID
                user_filter_status: user_filter_status.ACTIVE, // Ensure the filter is active
            },
        });

        // If the filter exists, proceed to mark it as deleted
        if (findFilterExist) {
            // Update the filter status to 'DELETED'
            const updateFilterStatus = await UserFilter.update(
                { user_filter_status: user_filter_status.DELETED }, // Update to DELETED status
                { where: { id: filter_id } } // Match the filter ID
            );

            // Check if the update operation affected any rows
            if (updateFilterStatus.length > 0) {
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("FILTER_DELETED_SUCCESSFULLY"), // Success message for deletion
                });
            } else {
                // Handle cases where the update failed
                return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("FAILED_TO_DELETE_FILTER"), // Failure message for deletion
                });
            }
        } else {
            // If the filter does not exist or is not active
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("FILTER_NOT_FOUND"), // Inform the user the filter was not found
                data: findFilterExist || {}, // Return empty data if filter is not found
            });
        }
    } catch (error) {
        // Log any errors that occur during execution for debugging purposes
        console.log(error);

        // Return a service unavailable status and a generic error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging
        });
    }
};

const getFilterColumnList = async (req: Request, res: Response) => {
    try {
        // Extract the filter_id from request parameters
        const { filter_id } = req.body;
        if (filter_id) {
            const findUserFilter = await UserFilter.findOne({ where: { id: filter_id, user_filter_status: user_filter_status.ACTIVE } })
            if (findUserFilter) {
                // Return the fetched filter list or an empty array if no filters exist
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("SUCCESS_FETCHED"), // Success message
                    data: findUserFilter && findUserFilter.group_value ? JSON.parse(findUserFilter.group_value) : {}
                });
            } else {
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("SUCCESS_FETCHED"), // Fail message
                    data: {}
                });
            }
        }
    } catch (error) {
        // Log any errors that occur during execution for debugging purposes
        console.log(error);

        // Return a service unavailable status and a generic error message
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"), // Generic error message
            data: error, // Include error data for debugging
        });
    }
};



export default { getFilterList, getFilterCategoryList, saveReportFilter, getUserReportFilter, getUserReportFilterList, deleteUserReportFilter, updateReportFilter, getFilterColumnList }