"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
// import { Role } from "./MORole";
import { MOModule } from "./MOModule";
import { addActivity } from "../helper/queue.service";

interface permissionAttributes {
  id: number;
  role_id: number;
  module_id?: number;
  widget_id?: number;
  permission: number;
  partial: boolean;
  status: status;
  created_by: number;
  updated_by: number;
  organization_id: string;
  order: number;
}

export enum status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export class MOPermission
  extends Model<permissionAttributes, never>
  implements permissionAttributes
{
  id!: number;
  role_id!: number;
  module_id?: number;
  widget_id?: number;
  permission!: number;
  partial!: boolean;
  status!: status;
  order!: number;
  created_by!: number;
  updated_by!: number;
  organization_id!: string;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    return this;
  }
}

MOPermission.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    module_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    permission: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    partial: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: Object.values(status),
      allowNull: false,
      defaultValue: status.ACTIVE,
    },
    widget_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mo_widgets',
        key: 'id'
      }
    },
    order: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
    organization_id: {
      type: DataTypes.STRING,
    },
  },
  {
    sequelize: sequelize,
    tableName: "mo_permissions",
    modelName: "MOPermission",
    timestamps: true,
  }
);


MOModule.hasMany(MOPermission, { foreignKey: "module_id" });
MOPermission.belongsTo(MOModule, { foreignKey: "module_id", as: "module" });

// Import and setup widget relationship
import { MOWidget } from "./MOWidget";
MOWidget.hasMany(MOPermission, { foreignKey: "widget_id" });
MOPermission.belongsTo(MOWidget, { foreignKey: "widget_id", as: "widget" });

MOPermission.addHook("afterUpdate", async (permission: any) => {
  await addActivity("MOPermission", "updated", permission);
});

MOPermission.addHook("afterCreate", async (permission: MOPermission) => {
  await addActivity("MOPermission", "created", permission);
});

MOPermission.addHook("beforeBulkUpdate", async (options: any) => {
  options.individualHooks = true;
});
