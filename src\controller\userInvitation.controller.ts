import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op, QueryTypes, Sequelize } from "sequelize";
import { GeneratePassword, roleName, sendInvitation, validateModulePermission } from "../helper/common";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { sequelize } from "../models";
import { Branch } from "../models/Branch";
import { Department } from "../models/Department";
import { User, user_status } from "../models/User";
import { invitation_status, UserInvite } from "../models/UserInvite";
import { UserRole } from "../models/UserRole";
import { RABBITMQ_QUEUE, ROLE_PERMISSIONS } from "../helper/constant";
import rabbitmqPublisher from '../rabbitmq/rabbitmq';


const sendInivitation = async (req: Request, res: Response) => {
    try {
        // Check module permission for staff invitation
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'staff_invitation',
            ROLE_PERMISSIONS.CREATE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res.status(StatusCodes.FORBIDDEN).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        const { user_ids } = req.body
        const isInvited = await sendInvitation(user_ids, req.user.id)
        let message = res.__("SELECTED_USER_INVITED")
        if (isInvited) {
            if (user_ids.length == 1) {
                message = res.__("USER_INVITED_SUCCESSFULLY")
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: message,
                });
            }
            return res.status(StatusCodes.OK).json({
                status: true,
                message: message,
            });
        } else {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("USER_INVITE_FAIL"),
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};
const sendReInvitation = async (req: Request, res: Response) => {
    try {
        const { user_ids } = req.body
        const findPendingUser = await User.findAll({
            attributes: ['id', 'keycloak_auth_id', 'user_first_name', 'user_last_name', 'user_email'],
            where: {
                id: { [Op.in]: user_ids },
                user_status: user_status.PENDING,
                organization_id: req.user.organization_id
            }, raw: true
        });
        let findName: any = ''
        for (const user of findPendingUser) {
            if (user.keycloak_auth_id) {
                const findInvitedUser: any = await UserInvite.findOne({ where: { user_id: user.id, invitation_status: { [Op.in]: [invitation_status.REINVITED, invitation_status.INVITED] } }, raw: true })
                if (findInvitedUser) {
                    if (findInvitedUser.invitation_status == invitation_status.INVITED) {
                        await UserInvite.update({ invitation_status: invitation_status.REINVITED, action_by: req.user.id, updated_by: req.user.id }, { where: { id: findInvitedUser.id } })
                    } else {
                        await UserInvite.update({ invitation_status: invitation_status.REINVITED, action_by: req.user.id, updated_by: req.user.id }, { where: { id: findInvitedUser.id } })
                    }
                } else {
                    await UserInvite.create({ user_id: user.id, invitation_status: invitation_status.INVITED, created_by: req.user.id, updated_by: req.user.id, action_by: req.user.id } as any)
                }

                const getRole = await UserRole.findAll({ where: { user_id: user.id }, raw: true })
                if (getRole) {
                    const role_ids = getRole.map((role) => role.role_id)
                    findName = await roleName(role_ids);
                }

                const randomPassword = await GeneratePassword()
                /** Prepare message for queue */
                const message = {
                    name: `${user.user_first_name} ${user.user_last_name}`,
                    email: user.user_email,
                    keycloak_auth_id: user.keycloak_auth_id,
                    type: 'staff_reinvite',
                    user_password: randomPassword,
                    addUserId: user_ids,
                    adminId: req.user.id,
                    role: findName.join(", ")
                };
                /** Publish a message to the "staff creation/update" queue */
                const queue: any = RABBITMQ_QUEUE.STAFF_CREATION_DETAILS;
                await rabbitmqPublisher.publishMessage(queue, message);
            }
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("USER_REINVITED_SUCCESSFULLY"),
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};
const getUserInviteList = async (req: Request, res: Response) => {
    try {
        const { page, size, search, invitation_date, branch_id, department_id, role_id, invitation_status }: any = req.query
        const { limit, offset } = getPagination(Number(page), Number(size));
        const userWhereObj: any = {
            organization_id: req.user.organization_id
        }
        // Search
        if (search) {
            userWhereObj[Op.or] = [
                Sequelize.where(
                    Sequelize.fn(
                        "concat",
                        Sequelize.col("user_first_name"),
                        " ",
                        Sequelize.col("user_last_name"),
                    ),
                    {
                        [Op.like]: `%${search}%`,
                    },
                ),
                Sequelize.where(
                    Sequelize.col("user_email"),
                    {
                        [Op.like]: `%${search}%`,
                    },
                )
            ];
        }

        if (branch_id) {
            userWhereObj.branch_id = branch_id
        }

        if (department_id) {
            userWhereObj.department_id = department_id
        }

        const includeArray: any = [{
            model: User,
            as: "user_invite",
            attributes: [
                "id",
                [
                    sequelize.literal(
                       `CASE 
            WHEN user_invite.user_avatar IS NULL OR user_invite.user_avatar = '' THEN ''
            WHEN NOT user_invite.user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_invite.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_invite.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_invite.user_avatar))
          END`
                    ),
                    "user_avatar_link",
                ],
                "user_avatar",
                [
                    sequelize.fn(
                        "concat",
                        sequelize.col("user_first_name"),
                        " ",
                        sequelize.col("user_last_name")
                    ),
                    "user_full_name",
                ],
                "user_email",
                "branch_id",
                "department_id",
                "employment_number",
            ],
            include: [
                {
                    model: Branch,
                    as: "branch",
                    attributes: ["id", "branch_name"],
                    // where: { organization_id: req.user.organization_id }
                },
                {
                    model: Department,
                    as: "department",
                    attributes: ["id", "department_name"],
                    // where: { organization_id: req.user.organization_id }
                }
            ],
            where: userWhereObj,
        }];
        const whereObj: any = { include: includeArray, where: {}, attributes: ['invitation_status', 'updatedAt'], order: [['updatedAt', 'DESC']] }
        const getUserDetail: any = await User.findOne({
            attributes: ['id', 'web_user_active_role_id'],
            where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
        });
        // Check if nv_roles table exists
        const checkTableExistQuery = `SHOW TABLES LIKE 'nv_roles';`;
        const tableExists = await sequelize.query(checkTableExistQuery, {
            type: QueryTypes.SHOWTABLES
        });

        if (!tableExists || tableExists.length === 0) {
            throw new Error("Table 'nv_roles' doesn't exist in database.");
        }
        // Construct recursive query to find child roles
        const getChildRolesQuery = `
        WITH RECURSIVE ChildRoles AS (
        SELECT id, role_name, parent_role_id
        FROM nv_roles WHERE id = :activeRoleId
        UNION ALL
        SELECT r.id, r.role_name, r.parent_role_id
        FROM nv_roles r
        INNER JOIN ChildRoles cr ON r.parent_role_id = cr.id
        )
        SELECT id
        FROM ChildRoles
        WHERE id != :activeRoleId`;

        // Execute recursive query to find child roles
        const getChildRoles = await sequelize.query(getChildRolesQuery, {
            replacements: { activeRoleId: getUserDetail.web_user_active_role_id },
            type: QueryTypes.SELECT,
        });

        // Build WHERE clause for user roles based on child roles
        let whereStr = '';
        getChildRoles.forEach((child_role: any, index: number) => {
            if (index > 0) {
                whereStr += ' OR ';
            }
            whereStr += `FIND_IN_SET(${child_role.id}, (SELECT GROUP_CONCAT(role_id) FROM nv_user_roles WHERE user_id = UserInvite.user_id)) > 0`;
        });
        if (whereStr) {
            whereObj.where[Op.and] = [sequelize.literal(`(${whereStr})`)];
        }
        if (invitation_status) {
            whereObj.where.invitation_status = invitation_status
        }

        if (page && size) {
            whereObj.limit = Number(limit)
            whereObj.offset = Number(offset)
        }

        if (role_id) {
            includeArray[0].include.push({
                model: UserRole,
                as: "user_roles",
                attributes: ["role_id"],
                where: { role_id: role_id },
                required: true,
            });
        }
        if (invitation_date) {
            whereObj.where.updatedAt = sequelize.where(sequelize.fn('DATE', sequelize.col('UserInvite.updatedAt')), {
                [Op.eq]: invitation_date
            });
        }
        const { count, rows: getInvitedUser } = await UserInvite.findAndCountAll(whereObj)

        if (getInvitedUser.length > 0) {
            const { total_pages } = getPaginatedItems(
                size,
                page,
                count || 0,
            );

            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                data: getInvitedUser,
                page: parseInt(page),
                size: parseInt(size),
                count: count,
                total_pages,
            });
        } else {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("FAIL_DATA_FETCHED"),
                data: [],
                page: 0,
                size: 0,
                count: 0,
                total_pages: 0,
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


export default {
    sendInivitation,
    getUserInviteList,
    sendReInvitation
};