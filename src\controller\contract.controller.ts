import { Request, Response } from 'express';
import { Op, Sequelize } from 'sequelize';
import { getPaginatedItems, getPagination } from '../helper/utils';
import { EmpContractCategory, type as categoryType } from '../models/EmployeeContractCategory';
import { EmpContractTemplate, status as templateStatus } from '../models/EmployeeContractTemplate';
import { EmpContractTemplateVersion } from '../models/EmployeeContractTemplateVersion';
import { Department } from '../models/Department';
import { StatusCodes } from 'http-status-codes';
import { sequelize } from '../models';
import { UserEmploymentContract, contract_status } from '../models/UserEmployementContract';
import { User, user_status } from '../models/User';
import { UserMeta } from '../models/UserMeta';
import { HrmcForm } from '../models/HrmcForm';
import moment from 'moment';
import path from 'path';
import fs from 'fs';
import { calculateExpiryDate, createNotification, generateS3EmployeeContract, getBranchSettingObj, getGeneralSettingObj, getGeoDetails, getOrganizationLogo, handleLeaveAccrual, sendEmailNotification, validateModulePermission } from '../helper/common';
import { EMAIL_ADDRESS, FORMCONSTANT, NOTIFICATION_TYPE, NOTIFICATIONCONSTANT, ROLE_CONSTANT, REDIRECTION_TYPE, ROLE_PERMISSIONS } from '../helper/constant';
import { check_list_status, UserCheckList } from '../models/UserCheckList';
import { ContractTypeModel, wageType } from '../models/ContractType';
import { JobRoleModel, status as jobStatus } from '../models/JobRole';
import { status } from '../models/LeavePolicy';
import { LeaveTypeModel } from '../models/LeaveType';
import { request_status, UserRequest } from '../models/UserRequest';
import { ContractNameModel } from '../models/ContractNameModel';
import { LeaveAccuralPolicy } from '../models/LeaveAccuralPolicy';
import { user_leave_policy_status, UserLeavePolicy } from '../models/UserLeavePolicy';
import { Role } from '../models/Role';

const createOrUpdateFile = async (req: Request, res: Response) => {
    const { name, type, content, department_id, template_id, remark } = req.body;
    try {
        // Check module permission for employee contract management
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'employee_contract',
            template_id ? ROLE_PERMISSIONS.EDIT : ROLE_PERMISSIONS.CREATE
        );

        if (!checkModulePermission) {
            return res.status(StatusCodes.FORBIDDEN).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }
        const categoryWhere: any = {
            type,
            organization_id: req.user.organization_id
        }
        let categoryName = ""
        if (type === categoryType.DEPT && department_id) {
            categoryWhere.department_id = department_id
            const findDept = await Department.findByPk(department_id)
            if (findDept) {
                categoryName = findDept.department_name
            } else {
                return res.status(StatusCodes.NOT_FOUND).send({ status: false, message: res.__("DEPARTMENT_NOT_FOUND") })
            }
        } else {
            categoryName = type.charAt(0).toUpperCase() + type.slice(1)
        }
        let folder = await EmpContractCategory.findOne({ where: categoryWhere });
        if (!folder) {
            categoryWhere.name = categoryName
            categoryWhere.organization_id = req.user.organization_id
            categoryWhere.created_by = req.user.id
            categoryWhere.updated_by = req.user.id
            folder = await EmpContractCategory.setHeaders(req).create(categoryWhere);
        }

        let file;
        if (template_id) {
            file = await EmpContractTemplate.findByPk(template_id);
            if (!file) {
                return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("FILE_NOT_FOUND") });
            }
            const findEmpContractCategory = await EmpContractCategory.findOne({ where: { id: file.category_id, organization_id: req.user.organization_id }, raw: true })
            const findIsUsed = await UserMeta.findAll({
                where: {
                    [Op.or]: [
                        { general_template: template_id },
                        { department_template: template_id },
                        Sequelize.literal(`(${[template_id].map(id =>
                            `FIND_IN_SET(${id}, additional_template) > 0`
                        ).join(' OR ')})`)
                    ]
                }
            });
            if (findIsUsed.length > 0 && (findEmpContractCategory?.department_id != folder.department_id)) {
                const plural = findIsUsed.length == 1 ? '' : 's';
                return res.status(StatusCodes.BAD_REQUEST).send({ status: false, message: res.__("CANNOT_MOVE_CONTRACT_DEPARTMENT", { userLength: findIsUsed.length, plural }) })
            }

            const versionNumber = (await EmpContractTemplateVersion.count({ where: { emp_contract_template_id: template_id } }) as any) + 1;
            const updateObj = {
                name: name ? name : file.name,
                status: templateStatus.ACTIVE,
                updated_by: req.user.id,
                category_id: folder.id,
                remark: remark,
                content: content ? content : file.content
            }

            if (file.content != content) {
                await EmpContractTemplateVersion.setHeaders(req).create({
                    emp_contract_template_id: file.id,
                    versionNumber,
                    remark,
                    content: content,
                    updated_by: req.user.id,
                } as any);

                updateObj.content = content;
            }
            await EmpContractTemplate.setHeaders(req).update(updateObj, { where: { id: template_id } });
        } else {
            file = await EmpContractTemplate.setHeaders(req).create({
                name,
                content,
                remark,
                status: templateStatus.ACTIVE,
                category_id: folder.id,
                created_by: req.user.id,
                updated_by: req.user.id,
            } as any);

            await EmpContractTemplateVersion.setHeaders(req).create({
                emp_contract_template_id: file.id,
                versionNumber: 1,
                content,
                remark,
                updated_by: req.user.id,
            } as any);
        }

        return res.status(StatusCodes.CREATED).json({ status: true, message: template_id ? res.__("SUCCESS_CONTRACT_UPDATED") : res.__("SUCCESS_CONTRACT_CREATED") });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

// Copy file into duplicate
const copyInDuplicateFile = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const file = await EmpContractTemplate.setHeaders(req).findOne({ where: { id }, raw: true });
        if (!file) {
            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("FILE_NOT_FOUND") });
        }

        const copyFile = await EmpContractTemplate.setHeaders(req).create({
            name: file.name + ' (copy)',
            content: file.content,
            remark: file.remark,
            status: templateStatus.ACTIVE,
            category_id: file.category_id,
            created_by: req.user.id,
            updated_by: req.user.id,
        } as any);

        await EmpContractTemplateVersion.setHeaders(req).create({
            emp_contract_template_id: copyFile.id,
            versionNumber: 1,
            content: file.content,
            remark: file.remark,
            updated_by: req.user.id,
        } as any);

        return res.status(StatusCodes.CREATED).json({ status: true, message: res.__("SUCCESS_CONTRACT_COPIED") });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const moveFileToCategory = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { category_id } = req.body;
        const file = await EmpContractTemplate.setHeaders(req).findOne({ where: { id }, raw: true });
        if (!file) {
            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("FILE_NOT_FOUND") });
        }

        const category = await EmpContractCategory.setHeaders(req).findOne({ where: { id: category_id, organization_id: req.user.organization_id }, raw: true });
        if (!category) {
            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__("CATEGORY_NOT_FOUND") });
        }

        const findIsUsed = await UserMeta.findAll({
            where: {
                [Op.or]: [
                    { general_template: id },
                    { department_template: id },
                    Sequelize.literal(`(${[id].map(id =>
                        `FIND_IN_SET(${id}, additional_template) > 0`
                    ).join(' OR ')})`)
                ]
            }
        });
        if (findIsUsed.length > 0) {
            const plural = findIsUsed.length == 1 ? '' : 's';
            return res.status(StatusCodes.BAD_REQUEST).send({ status: false, message: res.__("CANNOT_MOVE_CONTRACT_DEPARTMENT", { userLength: findIsUsed.length, plural }) })
        }

        const updateObj = {
            category_id: category_id,
            updated_by: req.user.id,
        }
        await EmpContractTemplate.setHeaders(req).update(updateObj, { where: { id } });
        return res.status(StatusCodes.OK).json({ status: true, message: res.__("SUCCESS_CONTRACT_MOVED") });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

// Get Folders with Pagination and Filters
const getFolders = async (req: Request, res: Response) => {
    const { page, size } = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));

    try {
        const whereObj: any = {
            where: { organization_id: req.user.organization_id },
            attributes: ["id", [sequelize.literal(`
                IF(
                  EmpContractCategory.type = "${categoryType.GENERAL}", 
                  EmpContractCategory.name, 
                  (SELECT department_name FROM nv_departments WHERE id = EmpContractCategory.department_id)
                )
              `), "name"], "type", "department_id", "updated_by", "updatedAt"],
        }
        if (page && size) {
            whereObj.limit = Number(limit)
            whereObj.offset = Number(offset)
        }
        const folders = await EmpContractCategory.findAndCountAll(whereObj);
        const response = getPaginatedItems(Number(size), Number(page), folders?.count);
        const data = {
            data: folders.rows,
            ...response
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

// Get All Files with Pagination and Filters
const getAllFiles = async (req: Request, res: Response) => {
    const { page, size, startDate, endDate, user_id, category_id, department_id, type, search, status, template_id }: any = req.query;

    // Check module permission for employee contract viewing
    const checkModulePermission = await validateModulePermission(
        req.user,
        req.user.organization_id,
        'employee_contract',
        ROLE_PERMISSIONS.VIEW
    );

    if (!checkModulePermission) {
        return res.status(StatusCodes.FORBIDDEN).json({
            status: false,
            message: res.__("PERMISSION_DENIED")
        });
    }

    const { limit, offset } = getPagination(page, size);
    const where: any = {};
    const categoryWhere: any = { organization_id: req.user.organization_id }

    if (startDate && endDate) {
        where.createdAt = { [Op.between]: [new Date(startDate as string), new Date(endDate as string)] };
    }

    if (user_id) {
        where.created_by = user_id;
    }

    if (category_id) {
        where.category_id = category_id;
    }

    if (template_id) {
        where.id = template_id;
    }

    if (type && Object.values(categoryType).includes(type)) {
        categoryWhere.type = type;
    }
    if (department_id) {
        categoryWhere.department_id = department_id
    }

    if (status && Object.values(templateStatus).includes(status)) {
        where.status = status;
    } else {
        where.status = { [Op.not]: templateStatus.DELETED }
    }

    if (search && search != "") {
        where.name = { [Op.like]: `%${search}%` }
    }



    const findObj: any = {
        attributes: ["id", "name", "content", "updated_by", "updatedAt", [sequelize.literal(`
      CASE
        WHEN LENGTH(content) < 1024 THEN CONCAT(LENGTH(content), 'B')
        WHEN LENGTH(content) < 1024 * 1024 THEN CONCAT(ROUND(LENGTH(content) / 1024, 2), 'KB')
        WHEN LENGTH(content) < 1024 * 1024 * 1024 THEN CONCAT(ROUND(LENGTH(content) / (1024 * 1024), 2), 'MB')
        ELSE CONCAT(ROUND(LENGTH(content) / (1024 * 1024 * 1024), 2), 'GB')
      END
    `), 'content_size'], [sequelize.literal(`'doc'`), 'type'], "status", "remark"],
        where,
        include: {
            model: EmpContractCategory,
            as: "emp_contract_category",
            where: categoryWhere,
            attributes: ["id", [sequelize.literal(`
        IF(
          emp_contract_category.type = "${categoryType.GENERAL}", 
          emp_contract_category.name, 
          (SELECT department_name FROM nv_departments WHERE id = emp_contract_category.department_id)
        )
      `), "name"], "type", "department_id"]
        },
        order: [["updatedAt", "DESC"]],
    }
    if (page && size) {
        findObj.limit = Number(limit);
        findObj.offset = Number(offset);
    }
    try {
        const files = await EmpContractTemplate.findAndCountAll(findObj);
        const response = getPaginatedItems(Number(size), Number(page), files?.count);
        const data: any = {
            data: files.rows,
            ...response
        }
        if (files?.count > 0) {
            data.data = files.rows.map(row => row.get({ plain: true }));
            for (const template of data.data) {
                const getUserMeta = await UserMeta.findAll({
                    attributes: ['user_id', 'general_template', 'department_template', 'additional_template'],
                    where: {
                        [Op.or]: [
                            { general_template: template.id },
                            { department_template: template.id },
                            {
                                additional_template: {
                                    [Op.like]: `%${template.id}%`
                                }
                            }
                        ],
                        user_id: {
                            [Op.in]: sequelize.literal(`(SELECT id FROM nv_users WHERE nv_users.id = UserMeta.user_id AND nv_users.organization_id = '${req.user.organization_id}' and nv_users.user_status NOT IN ('${user_status.DELETED}','${user_status.CANCELLED}'))`)
                        }
                    }
                });

                // Separate arrays for different roles
                const general_user_id: number[] = [];
                const department_user_id: number[] = [];
                const additional_user_id: number[] = [];

                // Create a lookup object to track unique users by ID
                const userMap: Record<string, {
                    user_id: number;
                    general_template: number | null;
                    department_template: number | null;
                    additional_template: string | null;
                }> = {};

                getUserMeta.forEach(user => {
                    const userId = user.user_id;

                    if (!userMap[userId]) {
                        userMap[userId] = {
                            user_id: user.user_id,
                            general_template: user.general_template,
                            department_template: user.department_template,
                            additional_template: user.additional_template
                        };
                    }

                    if (user.general_template === template.id) {
                        general_user_id.push(userId);
                    }
                    if (user.department_template === template.id) {
                        department_user_id.push(userId);
                    }
                    if (user.additional_template) {
                        if (user.additional_template.split(',').map(Number).includes(template.id)) {
                            additional_user_id.push(userId);
                        }
                    }
                });

                // Add these arrays to each template
                template.general_user_id = [...new Set(general_user_id)];
                template.department_user_id = [...new Set(department_user_id)];
                template.additional_user_id = [...new Set(additional_user_id)];
            }
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


// Get All File Versions with Pagination and Filters
const getAllFileVersions = async (req: Request, res: Response) => {
    const { page = 1, size = 10, startDate, endDate, user_id, template_id, versionNumber } = req.query;

    const { limit, offset } = getPagination(Number(page), Number(size));
    const where: any = {};

    if (startDate && endDate) {
        where.updatedAt = { [Op.between]: [new Date(startDate as string), new Date(endDate as string)] };
    }

    if (user_id) {
        where.updated_by = user_id;
    }
    if (template_id) {
        where.emp_contract_template_id = template_id;
    }

    if (versionNumber) {
        where.versionNumber = versionNumber
    }

    try {
        const whereObj: any = {
            attributes: ["id", "emp_contract_template_id", [sequelize.literal(`(SELECT name FROM nv_contract_template WHERE id = EmpContractTemplateVersion.emp_contract_template_id)`), "name"], "versionNumber", "content", "remark", "updatedAt", "updated_by"],
            where, limit, offset, order: [["versionNumber", "DESC"], ["id", "DESC"]],
        }
        const versions = await EmpContractTemplateVersion.findAndCountAll(whereObj);
        const response = getPaginatedItems(Number(size), Number(page), versions?.count);
        const data = {
            data: versions.rows,
            ...response
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

const deleteFilesFolder = async (req: Request, res: Response) => {
    try {
        const { template_ids, status } = req.body;
        const findTemplates = await EmpContractTemplate.findAll({ where: { id: { [Op.in]: template_ids } } });
        if (findTemplates.length > 0) {
            for (const template of findTemplates) {

                const findIsUsed = await UserMeta.findAll({
                    where: {
                        [Op.or]: [{ general_template: template.id }, { department_template: template.id }, Sequelize.literal(`(${[template?.id].map(id =>
                            `FIND_IN_SET(${id}, additional_template) > 0`
                        ).join(' OR ')})`)]
                    }
                });
                if (status == templateStatus.DELETED) {
                    if (!findIsUsed.length) {
                        await EmpContractTemplate.update({ status: status }, { where: { id: template.id } })
                    } else {
                        return res.status(StatusCodes.BAD_REQUEST).send({ status: false, message: res.__("DELETE_TEMPLATE_FAIL", { templateName: template.name }) })
                    }
                } else {
                    await EmpContractTemplate.update({ status: status }, { where: { id: template.id } })
                }
            }
        }
        return res.status(StatusCodes.OK).json({ status: true, message: status == templateStatus.INACTIVE ? res.__("SUCCESS_FILE_INACTIVATED") : status == templateStatus.ACTIVE ? res.__("SUCCESS_FILE_ACTIVATED") : res.__("SUCCESS_CONTRACT_DELETED") })
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}


const getAllUserContractVersion = async (req: Request, res: Response) => {
    try {
        const { page, size, user_id, search, status, startDate, endDate } = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));
        let userWhere: any = {
            organization_id: req.user.organization_id
        }
        if (search && search != "") {
            userWhere = {
                organization_id: req.user.organization_id,
                [Op.and]: [{
                    [Op.or]: [Sequelize.where(
                        Sequelize.fn(
                            "concat",
                            Sequelize.col("user_first_name"),
                            " ",
                            Sequelize.col("user_last_name"),
                        ),
                        {
                            [Op.like]: `%${search}%`,
                        },
                    ),
                    Sequelize.where(
                        Sequelize.col("user_email"),
                        {
                            [Op.like]: `%${search}%`,
                        },
                    )]
                }]
            }
        }

        const contractQuery = `  (
            IF (
              EXISTS (
                SELECT 1
                FROM nv_user_meta 
                WHERE user_id = user_employment_contract.id
                AND nv_user_meta.probation_length IS NOT NULL 
                AND nv_user_meta.probation_length != 0
                AND DATE_ADD(user_employment_contract.user_joining_date, INTERVAL nv_user_meta.probation_length DAY) > CURDATE()
              ), true, false
            )
          )`


        const whereObj: any = {
            attributes: ['id', 'user_id', [
                sequelize.literal(`
                  CASE 
                    WHEN (SELECT item_location FROM nv_items WHERE id = contract_with_sign) IS NOT NULL 
                    THEN CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = contract_with_sign))
                    ELSE NULL
                  END
                `),
                'contract_with_sign',
            ], 'contract_status', 'is_confirm_sign', 'expire_date', 'updated_by', 'updatedAt', 'createdAt', 'general_template', 'department_template', 'additional_template', 'tips_grade', 'expire_duration', 'wages_hours', 'leave_type_id'],
            where: { contract_status: user_id && user_id != '' ? { [Op.not]: contract_status.DELETED } : contract_status.ACTIVE },
            include: {
                model: User,
                as: 'user_employment_contract',
                attributes: ['id', [
                    sequelize.fn(
                        "concat",
                        sequelize.col("user_first_name"),
                        " ",
                        sequelize.col("user_last_name"),
                    ),
                    "user_full_name",
                ], 'user_status', 'user_email', [
                        sequelize.literal(
                            `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
          END`
                        ),
                        "user_avatar",
                    ],
                    [
                        sequelize.literal(contractQuery), 'is_probation'
                    ],
                    "employment_number"
                ],
                where: {
                    ...userWhere,
                    id: {
                        [Op.in]: sequelize.literal(`
                            (SELECT user_id 
                            FROM nv_user_roles 
                            GROUP BY user_id 
                            HAVING COUNT(DISTINCT role_id) > 1 
                            OR MIN(role_id) != 2)
                        `)
                    }
                },
                required: true,
            },
            order: [['expire_date', 'asc']],
            raw: true,
            nest: true
        }
        if (startDate && endDate) {
            whereObj.where.expire_date = { [Op.between]: [new Date(startDate as string), new Date(endDate as string)] };
        } else if (startDate != "" && endDate == "") {
            whereObj.where.expire_date = { [Op.gte]: new Date(startDate as string) };
        } else if (startDate == "" && endDate != "") {
            whereObj.where.expire_date = { [Op.lte]: new Date(endDate as string) };
        }
        if (page && size) {
            whereObj.limit = Number(limit);
            whereObj.offset = Number(offset);
        }

        if (status && status !== "") {
            const currentDate = moment().toDate();
            const futureDate = moment().add(15, 'days').toDate();

            switch (status) {
                case "expired":
                    whereObj.where.expire_date = { [Op.lte]: currentDate };
                    break;
                case "expiry-soon":
                    whereObj.where.expire_date = {
                        [Op.gt]: currentDate,
                        [Op.lte]: futureDate
                    };
                    break;
                case "active":
                    whereObj.where.contract_status = contract_status.ACTIVE
                    whereObj.where.expire_date = { [Op.or]: [{ [Op.gte]: futureDate }, null] }
                    whereObj.where.is_confirm_sign = true
                    whereObj.where = { ...whereObj.where, [Op.and]: Sequelize.where(sequelize.literal(contractQuery), { [Op.eq]: 0 }) }
                    break;
                case "probation":
                    whereObj.where.contract_status = contract_status.ACTIVE
                    whereObj.where.expire_date = { [Op.or]: [{ [Op.gte]: futureDate }, null] }
                    whereObj.where.is_confirm_sign = true
                    whereObj.where = { ...whereObj.where, [Op.and]: Sequelize.where(sequelize.literal(contractQuery), { [Op.eq]: 1 }) }
                    break;
                case "awaiting-signature":
                    whereObj.where.contract_status = contract_status.ACTIVE
                    whereObj.where.expire_date = { [Op.or]: [{ [Op.gte]: futureDate }, null] }
                    whereObj.where.is_confirm_sign = false
                    break;
                default:
                    whereObj.where.contract_status = status;
                    break;
            }
        }

        if (user_id && user_id != '') {
            whereObj.where.user_id = user_id;
            whereObj.order = [['createdAt', 'desc']]
        }
        let versions: any = await UserEmploymentContract.findAndCountAll(whereObj)
        versions = JSON.parse(JSON.stringify(versions))
        const response = getPaginatedItems(Number(size), Number(page), versions?.count);
        const data = {
            data: await Promise.all(versions.rows
                ?.map(async (user_contract: any) => {
                    // if (user_contract.contract_type) {
                    // }
                    return user_contract
                })),
            ...response
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}


const regenerateContractAfterUpdate = async (req: Request, res: Response) => {
    try {
        const { general_user_id = [], department_user_id = [], additional_user_id = [], removed_additional_user_id = [] }: any = req.body;
        const template_id = Number(req.body.template_id)

        const userWhere: any = {
            attributes: ['id', 'department_id', 'user_status', 'user_email', 'user_first_name', 'user_last_name', 'user_joining_date', 'user_avatar', 'address_line1', 'address_line2', 'geo_city', 'geo_state', 'geo_country', 'pin_code', 'country', 'branch_id', 'user_middle_name', 'appToken', 'webAppToken'],
            where: { id: { [Op.in]: [...general_user_id, ...department_user_id, ...additional_user_id] }, organization_id: req.user.organization_id, user_status: { [Op.in]: [user_status.ACTIVE, user_status.ONGOING, user_status.VERIFIED, user_status.COMPLETED, user_status.REJECTED, user_status.PENDING] } },
            raw: true,
            nest: true,
        }

        const findUsers = await User.findAll(userWhere)
        if (findUsers.length > 0) {
            for (const user of findUsers) {
                const userMeta = await UserMeta.findOne({ attributes: ['user_id', 'general_template', 'department_template', 'additional_template', 'expire_date', 'probation_length', 'wages_hours', 'fixed_types', 'wage_type', 'contract_remark', 'contract_name', 'leave_type_id', 'leave_days', 'leave_remark', 'leave_duration_type', 'place_of_work', 'contract_name_id', 'has_holiday_entitlement', 'holiday_entitlement_remark'], where: { user_id: user.id } });
                if (userMeta) {
                    const contract = await UserEmploymentContract.findOne({
                        where: { user_id: userMeta.user_id, contract_status: contract_status.ACTIVE }, order: [['id', 'desc']], raw: true
                    });

                    if (contract) {
                        const isGeneralTemplate = general_user_id.length > 0 ? general_user_id?.includes(userMeta.user_id) : false
                        const isDepartmentTemplate = department_user_id.length > 0 ? department_user_id.includes(userMeta.user_id) : false
                        const isAdditionalTemplate = additional_user_id.length > 0 ? additional_user_id.includes(userMeta.user_id) : false

                        let generalContent, deptContent, additionalLatestTemplate, additionalLatestTemplateIds;
                        let additionalDuties = ''
                        let jobTitle: any
                        const findTemplateCategory: any = await EmpContractTemplate.findOne({ where: { id: template_id, status: templateStatus.ACTIVE }, include: [{ model: EmpContractCategory, as: 'emp_contract_category' }], raw: true, nest: true })
                        const templateCategoryType = findTemplateCategory?.emp_contract_category?.type

                        if (isGeneralTemplate) {
                            generalContent = await EmpContractTemplateVersion.findOne({ attributes: ['id', 'content'], where: { emp_contract_template_id: template_id }, raw: true, order: [['id', 'desc']] });
                        } else {
                            generalContent = await EmpContractTemplateVersion.findOne({ attributes: ['id', 'content'], where: { id: contract.general_template }, raw: true });
                        }

                        if (isAdditionalTemplate) {
                            const findAdditionalTemplate = userMeta?.additional_template ? userMeta.additional_template?.split(",").map(Number) : [template_id]
                            if (findAdditionalTemplate.length > 0) {
                                if (!findAdditionalTemplate.includes(template_id)) {
                                    findAdditionalTemplate.push(template_id)
                                }
                                const findLatestAdditiArr = []
                                for (let i = 0; i < findAdditionalTemplate.length; i++) {
                                    const empContract = findAdditionalTemplate[i];
                                    const findTemplateCategory: any = await EmpContractTemplate.findOne({ where: { id: empContract, status: { [Op.not]: templateStatus.DELETED } }, raw: true, nest: true })
                                    if (findTemplateCategory) {
                                        additionalLatestTemplate = await EmpContractTemplateVersion.findOne({
                                            where: { emp_contract_template_id: empContract },
                                            order: [['id', 'desc']]
                                        });

                                        // Add the ID to the array
                                        findLatestAdditiArr.push(additionalLatestTemplate?.id);

                                        additionalDuties += (additionalDuties ? ' <br> ' : '') + additionalLatestTemplate?.content; // Append subsequent content
                                    }
                                }
                                additionalLatestTemplateIds = findLatestAdditiArr.toString();
                            }
                        } else {
                            const findAdditionalTemplate = contract?.additional_template ? contract.additional_template?.split(",").map(Number) : []
                            if (findAdditionalTemplate.length > 0) {
                                const findLatestAdditiArr = []
                                for (let i = 0; i < findAdditionalTemplate.length; i++) {
                                    const empContract = findAdditionalTemplate[i];
                                    additionalLatestTemplate = await EmpContractTemplateVersion.findOne({ where: { id: empContract }, order: [['id', 'desc']] })

                                    // Add the ID to the array
                                    findLatestAdditiArr.push(additionalLatestTemplate?.id);

                                    additionalDuties += (additionalDuties ? ' <br> ' : '') + additionalLatestTemplate?.content; // Append subsequent content
                                }
                                additionalLatestTemplateIds = findLatestAdditiArr.toString();
                            }
                        }


                        if (isDepartmentTemplate && (templateCategoryType == categoryType.DEPT) && (user.department_id == findTemplateCategory?.emp_contract_category?.department_id)) {
                            deptContent = await EmpContractTemplateVersion.findOne({ where: { emp_contract_template_id: template_id }, order: [['id', 'desc']] });
                            if (findTemplateCategory) {
                                jobTitle = findTemplateCategory.name ? findTemplateCategory.name : ''
                            }
                        } else {
                            deptContent = await EmpContractTemplateVersion.findOne({ where: { id: contract.department_template } });
                            if (findTemplateCategory) {
                                jobTitle = findTemplateCategory.name ? findTemplateCategory.name : ''
                            }
                        }


                        if (generalContent || deptContent || additionalLatestTemplateIds) {
                            const getHrmcDetail: any = await HrmcForm.findOne({
                                where: { checklist_id: 3, user_id: user.id },
                            });
                            const isTemplateUpdated = (userMeta && (contract.general_template != generalContent?.id) || (contract.department_template != deptContent?.id) || (contract?.additional_template != additionalLatestTemplateIds) || (contract.other != userMeta?.other) || (moment(userMeta?.expire_date).format("YYYY-MM-DD") != moment(contract?.expire_date).format("YYYY-MM-DD")) || (moment(user?.user_joining_date).format("YYYY-MM-DD") != moment(contract?.start_date).format("YYYY-MM-DD")) || (contract.wages_hours != userMeta?.wages_hours) || (contract.fixed_types != userMeta?.fixed_types) || (contract?.wage_type != userMeta?.wage_type) || (contract?.contract_remark != userMeta?.contract_remark) || (contract?.contract_name != userMeta?.contract_name) || (contract?.leave_type_id != userMeta?.leave_type_id) || (contract?.leave_days != userMeta?.leave_days) || (contract?.leave_remark != userMeta?.leave_remark) || (contract?.leave_duration_type != userMeta?.leave_duration_type) || (contract?.place_of_work != userMeta?.place_of_work) || (contract?.contract_name_id != userMeta?.contract_name_id) || (contract?.has_holiday_entitlement != userMeta?.has_holiday_entitlement) || (contract?.holiday_entitlement_remark != userMeta?.holiday_entitlement_remark)) || contract?.is_confirm_sign == true || (contract.probation_length != userMeta?.probation_length)

                            const fileName = `emp_contract_${user?.user_first_name}_${user?.user_last_name}_${user.id}_${moment().format('YYYY-MM-DD_HH-mm-sss')}.pdf`;

                            // Create a temporary local path for generating the PDF
                            const tempDir = path.resolve(__dirname, "..", "uploads", "temp");
                            if (!fs.existsSync(tempDir)) {
                                fs.mkdirSync(tempDir, { recursive: true });
                            }

                            const addressParts = [];

                            if (user?.address_line1) {
                                addressParts.push(user.address_line1);
                            }
                            if (user?.address_line2) {
                                addressParts.push(user.address_line2);
                            }
                            if (user?.geo_city) {
                                addressParts.push((await getGeoDetails(user.geo_city)).place_name);
                            }
                            if (user?.geo_state) {
                                addressParts.push((await getGeoDetails(user.geo_state)).place_name);
                            }
                            if (user?.geo_country) {
                                addressParts.push((await getGeoDetails(user.geo_country)).place_name);
                            }
                            if (user?.pin_code) {
                                addressParts.push(user.pin_code);
                            }

                            const employeeName = [];

                            if (user?.user_first_name) {
                                employeeName.push(user.user_first_name);
                            }
                            if (user?.user_middle_name) {
                                employeeName.push(user.user_middle_name);
                            }
                            if (user?.user_last_name) {
                                employeeName.push(user.user_last_name);
                            }
                            const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)
                            let findBranchSetting: any
                            if (user.branch_id) {
                                findBranchSetting = await getBranchSettingObj(user.branch_id)
                            }
                            let namaste_logo: any = `${global.config.API_UPLOAD_URL} + "/email_logo/logo.png",`
                            if (findGeneralSetting && findGeneralSetting.brand_logo_link) {
                                namaste_logo = findGeneralSetting.brand_logo_link
                            }
                            const leaveTypeObj: any = {
                                durationType: 'Days',
                                days: userMeta && userMeta?.leave_days ? userMeta?.leave_days : 0
                            };

                            const expireDate: any = userMeta?.expire_date == null ? userMeta?.expire_duration && userMeta?.expire_date ? await calculateExpiryDate(userMeta?.expire_date, userMeta?.expire_duration) : userMeta?.expire_date : userMeta?.expire_date

                            const compileData = {
                                NAMASTE_LOGO: namaste_logo,
                                branch_heading_employer_name: findBranchSetting && findBranchSetting.branch_employer_name ? findBranchSetting.branch_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "MicrOffice",
                                employer_name: findBranchSetting && findBranchSetting.branch_heading_employer_name ? findBranchSetting.branch_heading_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "MicrOffice",
                                branch_heading_name: findBranchSetting && findBranchSetting.branch_heading_name ? findBranchSetting.branch_heading_name : "MicrOffice",
                                branch_heading_work_place: findBranchSetting && findBranchSetting.branch_heading_work_place ? findBranchSetting.branch_heading_work_place : null,
                                work_place: userMeta && userMeta?.place_of_work ? userMeta.place_of_work : findBranchSetting && findBranchSetting.branch_work_place ? findBranchSetting.branch_work_place : null,
                                employee_name: employeeName.join(" "),
                                employee_address: addressParts.join(", "),
                                employee_sign: null,
                                employer_sign: findBranchSetting && findBranchSetting.branch_sign ? `${findBranchSetting.branch_sign}` : findGeneralSetting.employer_sign && findGeneralSetting.employer_sign != '' ? findGeneralSetting.employer_sign : null,
                                insurance_number: getHrmcDetail?.insurance_number,
                                job_title: jobTitle,
                                joining_date: user?.user_joining_date
                                    ? moment(user?.user_joining_date).format("DD/MM/YYYY")
                                    : "",
                                date: moment().format("DD/MM/YYYY"),
                                generalContent: generalContent?.content,
                                deptContent: deptContent?.content,
                                addittionalContent: additionalDuties ? additionalDuties : "",
                                otherContent: userMeta?.other,
                                expire_date: expireDate ? moment(expireDate).format("DD/MM/YYYY") : "",
                                expire_duration: userMeta?.expire_duration,
                                tips_grade: userMeta?.tips_grade,
                                wages_per_hours: userMeta?.wages_hours ? userMeta?.wages_hours : null,
                                wages_type: userMeta?.wage_type,
                                probation_period: userMeta?.probation_length,
                                fixed_types: userMeta?.wage_type && userMeta.wage_type === wageType.FIXED && userMeta?.fixed_types ? userMeta?.fixed_types : userMeta?.fixed_types,
                                annual_holiday: userMeta?.has_holiday_entitlement ? leaveTypeObj ? leaveTypeObj : null : null,
                                contract_type_name: userMeta?.contract_name ? userMeta?.contract_name : "",
                                nationality: user?.country ? user?.country : "",
                                registration_number: findBranchSetting && findBranchSetting?.registration_number ? findBranchSetting?.registration_number : null,
                                contract_type: JSON.parse(JSON.stringify(userMeta)),
                            };

                            const generatedPdf = await generateS3EmployeeContract(
                                fileName,
                                compileData,
                                FORMCONSTANT.EMPLOYE_CONTRACT.template,
                                req.user.organization_id,
                                user?.id?.toString()
                            );

                            // // Generate PDF contract locally first
                            // await generateEmployeeContract(
                            //     tempFilePath,
                            //     compileData,
                            //     FORMCONSTANT.EMPLOYE_CONTRACT.template,
                            // );

                            // // Upload to S3 and create Item record
                            // const bucketName = process.env.NODE_ENV || "development";
                            // const fileBuffer = fs.readFileSync(tempFilePath);
                            // const s3FilePath = FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                            //     req.user.organization_id,
                            //     user.id.toString(),
                            //     fileName
                            // );

                            // // Generate hash for the file
                            // const fileHash: any = await getHash(fileBuffer, {
                            //     originalname: fileName,
                            //     mimetype: "application/pdf"
                            // });

                            // // Check if file already exists by hash
                            // let itemId;
                            // let fileExists = false;

                            // if (fileHash.status) {
                            //     const existingItem = await Item.findOne({
                            //         where: {
                            //             item_hash: fileHash.hash,
                            //             item_organization_id: req.user.organization_id
                            //         }
                            //     });

                            //     if (existingItem) {
                            //         fileExists = true;
                            //         itemId = existingItem.id;
                            //     }
                            // }

                            // // If file doesn't exist, upload to S3 and create Item record
                            // if (!fileExists) {
                            //     // Upload file to S3
                            //     await s3.send(
                            //         new PutObjectCommand({
                            //             Bucket: bucketName,
                            //             Key: s3FilePath,
                            //             Body: fileBuffer,
                            //             ContentType: "application/pdf"
                            //         })
                            //     );

                            //     // Create Item record
                            //     const itemData = {
                            //         item_type: item_type.PDF,
                            //         item_name: fileName,
                            //         item_hash: fileHash.hash,
                            //         item_mime_type: "application/pdf",
                            //         item_extension: ".pdf",
                            //         item_size: fileBuffer.length,
                            //         item_IEC: item_IEC.B,
                            //         item_status: item_status.ACTIVE,
                            //         item_external_location: item_external_location.NO,
                            //         item_location: s3FilePath,
                            //         item_organization_id: req.user.organization_id
                            //     };

                            //     const newItem = await Item.create(itemData as any);
                            //     itemId = newItem.id;
                            // }

                            // // Remove temporary file
                            // fs.unlinkSync(tempFilePath);

                            // Update user with item ID instead of filename
                            await User.setHeaders(req).update(
                                { employment_contract: String(generatedPdf?.item_id) },
                                { where: { id: user.id } },
                            );

                            userMeta.isDraft = false
                            await userMeta.save()

                            if (isTemplateUpdated) {
                                const userMetaUpdate: any = {
                                    updated_by: req.user.id
                                }

                                if (isDepartmentTemplate && (templateCategoryType == categoryType.DEPT) && (user.department_id == findTemplateCategory?.emp_contract_category?.department_id)) {
                                    userMetaUpdate.department_template = template_id
                                }

                                if (isGeneralTemplate) {
                                    userMetaUpdate.general_template = template_id
                                }

                                if (isAdditionalTemplate) {
                                    const findAdditionalTemplate = userMeta.additional_template ? userMeta.additional_template.split(",").map(Number) : [template_id]
                                    if (!findAdditionalTemplate.includes(Number(template_id)) && templateCategoryType == categoryType.DEPT) {
                                        findAdditionalTemplate.push(template_id)
                                    }
                                    userMetaUpdate.additional_template = findAdditionalTemplate.join(',');

                                }
                                if (expireDate) {
                                    userMetaUpdate.expire_date = expireDate
                                }
                                await UserMeta.update(userMetaUpdate, { where: { user_id: userMeta.user_id } })

                                await UserEmploymentContract.update({ contract_status: contract_status.INACTIVE, updated_by: req.user.id }, { where: { user_id: userMeta.user_id, contract_status: contract_status.ACTIVE } })

                                // Create with item ID instead of filename
                                await UserEmploymentContract.create({
                                    contract_with_sign: String(generatedPdf?.item_id),
                                    is_confirm_sign: false,
                                    contract_status: contract_status.ACTIVE,
                                    updated_by: req.user.id,
                                    created_by: req.user.id,
                                    general_template: generalContent?.id,
                                    department_template: deptContent?.id,
                                    additional_template: additionalLatestTemplateIds,
                                    tips_grade: userMeta?.tips_grade,
                                    other: userMeta?.other,
                                    expire_date: expireDate,
                                    user_joining_date: user?.user_joining_date,
                                    user_id: userMeta.user_id,
                                    fixed_types: userMeta?.fixed_types,
                                    working_hours: userMeta?.working_hours,
                                    duration_type: userMeta?.duration_type,
                                    wage_type: userMeta?.wage_type,
                                    contract_remark: userMeta?.contract_remark,
                                    contract_name: userMeta?.contract_name,
                                    leave_type_id: userMeta?.leave_type_id,
                                    leave_days: userMeta?.leave_days,
                                    leave_remark: userMeta?.leave_remark,
                                    leave_duration_type: userMeta?.leave_duration_type,
                                    place_of_work: userMeta?.place_of_work,
                                    contract_name_id: userMeta?.contract_name_id,
                                    has_holiday_entitlement: userMeta?.has_holiday_entitlement,
                                    holiday_entitlement_remark: userMeta?.holiday_entitlement_remark
                                } as any)

                                await UserCheckList.setHeaders(req).update({ status: check_list_status.PENDING }, { where: { to_user_id: userMeta.user_id, from_user_id: userMeta.user_id, checklist_id: 4 } });

                                await User.update({ user_status: user_status.ONGOING }, { where: { id: userMeta.user_id } })

                                const templateData = {
                                    name: user.user_first_name,
                                    admin_name: req.user?.user_first_name,
                                    email: user.user_email,
                                    mail_type: 'onboarding_regenerate',
                                    ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
                                    LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
                                    ADDRESS: EMAIL_ADDRESS.ADDRESS,
                                    PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                                    EMAIL: EMAIL_ADDRESS.EMAIL,
                                    ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                                    smtpConfig: 'INFO'
                                }
                                await sendEmailNotification(templateData)
                                await createNotification([user], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.content(req.user.user_first_name, "regenerated"), NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.heading, REDIRECTION_TYPE.ONBOARDING)
                            }
                        }
                    }
                }
            }
        }

        if (removed_additional_user_id.length > 0) {
            const userRemoveWhere: any = {
                attributes: ['id', 'department_id', 'user_status', 'user_email', 'user_first_name', 'user_last_name', 'user_joining_date', 'user_avatar', 'address_line1', 'address_line2', 'geo_city', 'geo_state', 'geo_country', 'pin_code', 'country', 'branch_id', 'user_middle_name', 'appToken', 'webAppToken'],
                where: { id: { [Op.in]: removed_additional_user_id }, organization_id: req.user.organization_id, user_status: { [Op.in]: [user_status.ACTIVE, user_status.ONGOING, user_status.VERIFIED, user_status.COMPLETED, user_status.REJECTED, user_status.PENDING] } },
                raw: true,
                nest: true,
            }
            const findRemovedUsers = await User.findAll(userRemoveWhere)
            if (findRemovedUsers.length > 0) {
                for (const user of findRemovedUsers) {
                    const userMeta = await UserMeta.findOne({ where: { user_id: user.id } });
                    if (userMeta) {
                        const contract = await UserEmploymentContract.findOne({
                            where: { user_id: userMeta.user_id, contract_status: contract_status.ACTIVE }, order: [['id', 'desc']]
                        });
                        if (contract) {

                            let generalContent, deptContent, additionalLatestTemplate, additionalLatestTemplateIds;
                            let additionalDuties = ''
                            let jobTitle: any
                            const findTemplateCategory: any = await EmpContractTemplate.findOne({ where: { id: template_id, status: templateStatus.ACTIVE }, include: [{ model: EmpContractCategory, as: 'emp_contract_category' }], raw: true, nest: true })

                            if (userMeta.general_template) {
                                generalContent = await EmpContractTemplateVersion.findOne({ where: { emp_contract_template_id: userMeta.general_template }, order: [['id', 'desc']] });
                            } else {
                                generalContent = await EmpContractTemplateVersion.findOne({ where: { id: contract.general_template } });
                            }

                            if (userMeta.additional_template) {
                                const findAdditionalTemplate = userMeta.additional_template?.split(",").map(Number)
                                if (findAdditionalTemplate.length > 0) {
                                    const findLatestAdditiArr = []
                                    if (findAdditionalTemplate.includes(template_id)) {
                                        findAdditionalTemplate.splice(findAdditionalTemplate.indexOf(template_id), 1)
                                    }
                                    for (let i = 0; i < findAdditionalTemplate.length; i++) {
                                        const empContract = findAdditionalTemplate[i];
                                        additionalLatestTemplate = await EmpContractTemplateVersion.findOne({
                                            where: { emp_contract_template_id: empContract },
                                            order: [['id', 'desc']]
                                        });

                                        // Add the ID to the array
                                        findLatestAdditiArr.push(additionalLatestTemplate?.id);

                                        additionalDuties += (additionalDuties ? ' <br> ' : '') + additionalLatestTemplate?.content; // Append subsequent content
                                    }
                                    additionalLatestTemplateIds = findLatestAdditiArr.toString();
                                }
                            } else {
                                const findAdditionalTemplate = contract.additional_template?.split(",").map(Number)
                                if (findAdditionalTemplate.length > 0) {
                                    const findLatestAdditiArr = []
                                    for (let i = 0; i < findAdditionalTemplate.length; i++) {
                                        const empContract = findAdditionalTemplate[i];
                                        additionalLatestTemplate = await EmpContractTemplateVersion.findOne({
                                            where: { emp_contract_template_id: empContract },
                                            order: [['id', 'desc']]
                                        });

                                        // Add the ID to the array
                                        findLatestAdditiArr.push(additionalLatestTemplate?.id);

                                        additionalDuties += (additionalDuties ? ' <br> ' : '') + additionalLatestTemplate?.content; // Append subsequent content
                                    }
                                    additionalLatestTemplateIds = findLatestAdditiArr.toString();
                                }
                            }

                            if (userMeta.department_template) {
                                deptContent = await EmpContractTemplateVersion.findOne({ where: { emp_contract_template_id: userMeta.department_template }, order: [['id', 'desc']] });
                                if (findTemplateCategory) {
                                    jobTitle = findTemplateCategory.name ? findTemplateCategory.name : ''
                                }
                            } else {
                                deptContent = await EmpContractTemplateVersion.findOne({ where: { id: contract.department_template } });
                                if (findTemplateCategory) {
                                    jobTitle = findTemplateCategory.name ? findTemplateCategory.name : ''
                                }
                            }


                            if (generalContent || deptContent || additionalLatestTemplateIds) {
                                const getHrmcDetail: any = await HrmcForm.findOne({
                                    where: { checklist_id: 3, user_id: user.id }, raw: true
                                });
                                const isTemplateUpdated = (userMeta && (contract.general_template != generalContent?.id) || (contract.department_template != deptContent?.id) || (contract?.additional_template != additionalLatestTemplateIds) || (contract.other != userMeta?.other) || (moment(userMeta?.expire_date).format("YYYY-MM-DD") != moment(contract?.expire_date).format("YYYY-MM-DD")) || (moment(user?.user_joining_date).format("YYYY-MM-DD") != moment(contract?.start_date).format("YYYY-MM-DD")) || (contract.wages_hours != userMeta?.wages_hours) || (contract.fixed_types != userMeta?.fixed_types) || (contract?.wage_type != userMeta?.wage_type) || (contract?.contract_remark != userMeta?.contract_remark) || (contract?.contract_name != userMeta?.contract_name) || (contract?.leave_type_id != userMeta?.leave_type_id) || (contract?.leave_days != userMeta?.leave_days) || (contract?.leave_remark != userMeta?.leave_remark) || (contract?.leave_duration_type != userMeta?.leave_duration_type) || (contract?.place_of_work != userMeta?.place_of_work) || (contract?.working_hour_per_day != userMeta?.working_hour_per_day) || (contract?.max_limit_per_week != userMeta?.max_limit_per_week) || (contract?.contract_name_id != userMeta?.contract_name_id) || (contract?.has_holiday_entitlement != userMeta?.has_holiday_entitlement) || (contract?.holiday_entitlement_remark != userMeta?.holiday_entitlement_remark)) || contract?.is_confirm_sign == true || (contract.probation_length != userMeta?.probation_length)

                                const fileName = `emp_contract_${user?.user_first_name}_${user?.user_last_name}_${user.id}_${moment().format('YYYY-MM-DD_HH-mm-sss')}.pdf`;

                                // Create a temporary local path for generating the PDF
                                const tempDir = path.resolve(__dirname, "..", "uploads", "temp");
                                if (!fs.existsSync(tempDir)) {
                                    fs.mkdirSync(tempDir, { recursive: true });
                                }

                                const addressParts = [];

                                if (user?.address_line1) {
                                    addressParts.push(user.address_line1);
                                }
                                if (user?.address_line2) {
                                    addressParts.push(user.address_line2);
                                }
                                if (user?.geo_city) {
                                    addressParts.push((await getGeoDetails(user.geo_city)).place_name);
                                }
                                if (user?.geo_state) {
                                    addressParts.push((await getGeoDetails(user.geo_state)).place_name);
                                }
                                if (user?.geo_country) {
                                    addressParts.push((await getGeoDetails(user.geo_country)).place_name);
                                }
                                if (user?.pin_code) {
                                    addressParts.push(user.pin_code);
                                }

                                const employeeName = [];

                                if (user?.user_first_name) {
                                    employeeName.push(user.user_first_name);
                                }
                                if (user?.user_middle_name) {
                                    employeeName.push(user.user_middle_name);
                                }
                                if (user?.user_last_name) {
                                    employeeName.push(user.user_last_name);
                                }
                                const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)
                                let findBranchSetting: any
                                if (user.branch_id) {
                                    findBranchSetting = await getBranchSettingObj(user.branch_id)
                                }
                                let namaste_logo: any = `${global.config.API_BASE_URL} + "/email_logo/logo.png",`
                                if (findGeneralSetting && findGeneralSetting.brand_logo_link) {
                                    namaste_logo = findGeneralSetting.brand_logo_link
                                }
                                const leaveTypeObj: any = {
                                    durationType: 'Days',
                                    days: userMeta && userMeta?.leave_days ? userMeta?.leave_days : 0
                                };
                                const expireDate: any = userMeta?.expire_duration && userMeta?.expire_date ? await calculateExpiryDate(userMeta?.expire_date, userMeta?.expire_duration) : userMeta?.expire_date

                                const compileData = {
                                    NAMASTE_LOGO: namaste_logo,
                                    branch_heading_employer_name: findBranchSetting && findBranchSetting.branch_employer_name ? findBranchSetting.branch_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "Micro Office",
                                    employer_name: findBranchSetting && findBranchSetting.branch_heading_employer_name ? findBranchSetting.branch_heading_employer_name : findGeneralSetting && findGeneralSetting.employer_name != '' ? findGeneralSetting.employer_name : "Micro Office",
                                    branch_heading_name: findBranchSetting && findBranchSetting.branch_heading_name ? findBranchSetting.branch_heading_name : "Micro Office",
                                    branch_heading_work_place: findBranchSetting && findBranchSetting.branch_heading_work_place ? findBranchSetting.branch_heading_work_place : null,
                                    work_place: userMeta && userMeta?.place_of_work ? userMeta.place_of_work : findBranchSetting && findBranchSetting.branch_work_place ? findBranchSetting.branch_work_place : null,
                                    employee_name: employeeName.join(" "),
                                    employee_address: addressParts.join(", "),
                                    employee_sign: null,
                                    employer_sign: findBranchSetting && findBranchSetting.branch_sign ? `${findBranchSetting.branch_sign}` : findGeneralSetting.employer_sign && findGeneralSetting.employer_sign != '' ? findGeneralSetting.employer_sign : null,
                                    insurance_number: getHrmcDetail?.insurance_number,
                                    job_title: jobTitle,
                                    joining_date: user?.user_joining_date
                                        ? moment(user?.user_joining_date).format("DD/MM/YYYY")
                                        : "",
                                    date: moment().format("DD/MM/YYYY"),
                                    generalContent: generalContent?.content,
                                    deptContent: deptContent?.content,
                                    addittionalContent: additionalDuties ? additionalDuties : "",
                                    otherContent: userMeta?.other,
                                    expire_date: expireDate ? expireDate : "",
                                    expire_duration: userMeta?.expire_duration,
                                    tips_grade: userMeta?.tips_grade,
                                    wages_per_hours: userMeta?.wages_hours ? userMeta?.wages_hours : null,
                                    wages_type: userMeta?.wage_type,
                                    probation_period: userMeta?.probation_length,
                                    fixed_types: userMeta?.wage_type && userMeta.wage_type === wageType.FIXED && userMeta?.fixed_types ? userMeta?.fixed_types : userMeta?.fixed_types,
                                    annual_holiday: userMeta?.has_holiday_entitlement ? leaveTypeObj ? leaveTypeObj : null : null,
                                    contract_type_name: userMeta?.contract_name ? userMeta?.contract_name : "",
                                    nationality: user?.country ? user?.country : "",
                                    registration_number: findBranchSetting && findBranchSetting?.registration_number ? findBranchSetting?.registration_number : null,
                                    contract_type: JSON.parse(JSON.stringify(userMeta)),
                                };

                                const generatedPdf = await generateS3EmployeeContract(
                                    fileName,
                                    compileData,
                                    FORMCONSTANT.EMPLOYE_CONTRACT.template,
                                    req.user.organization_id,
                                    user?.id?.toString()
                                );

                                // // Generate PDF contract locally first
                                // await generateEmployeeContract(
                                //     tempFilePath,
                                //     compileData,
                                //     FORMCONSTANT.EMPLOYE_CONTRACT.template,
                                // );

                                // // Upload to S3 and create Item record
                                // const bucketName = process.env.NODE_ENV || "development";
                                // const fileBuffer = fs.readFileSync(tempFilePath);
                                // const s3FilePath = FILE_UPLOAD_CONSTANT.ONBOARDING_FILES.destinationPath(
                                //     req.user.organization_id,
                                //     user.id.toString(),
                                //     fileName
                                // );

                                // // Generate hash for the file
                                // const fileHash: any = await getHash(fileBuffer, {
                                //     originalname: fileName,
                                //     mimetype: "application/pdf"
                                // });

                                // // Check if file already exists by hash
                                // let itemId;
                                // let fileExists = false;

                                // if (fileHash.status) {
                                //     const existingItem = await Item.findOne({
                                //         where: {
                                //             item_hash: fileHash.hash,
                                //             item_organization_id: req.user.organization_id
                                //         }
                                //     });

                                //     if (existingItem) {
                                //         fileExists = true;
                                //         itemId = existingItem.id;
                                //     }
                                // }

                                // // If file doesn't exist, upload to S3 and create Item record
                                // if (!fileExists) {
                                //     // Upload file to S3
                                //     await s3.send(
                                //         new PutObjectCommand({
                                //             Bucket: bucketName,
                                //             Key: s3FilePath,
                                //             Body: fileBuffer,
                                //             ContentType: "application/pdf"
                                //         })
                                //     );

                                //     // Create Item record
                                //     const itemData = {
                                //         item_type: item_type.PDF,
                                //         item_name: fileName,
                                //         item_hash: fileHash.hash,
                                //         item_mime_type: "application/pdf",
                                //         item_extension: ".pdf",
                                //         item_size: fileBuffer.length,
                                //         item_IEC: item_IEC.B,
                                //         item_status: item_status.ACTIVE,
                                //         item_external_location: item_external_location.NO,
                                //         item_location: s3FilePath,
                                //         item_organization_id: req.user.organization_id
                                //     };

                                //     const newItem = await Item.create(itemData as any);
                                //     itemId = newItem.id;
                                // }

                                // // Remove temporary file
                                // fs.unlinkSync(tempFilePath);

                                // Update user with item ID instead of filename
                                await User.setHeaders(req).update(
                                    { employment_contract: String(generatedPdf?.item_id) },
                                    { where: { id: user.id } },
                                );

                                userMeta.isDraft = false
                                await userMeta.save()
                                if (isTemplateUpdated) {
                                    const findAdditionalTemplate = userMeta.additional_template ? userMeta.additional_template.split(",").map(Number) : []
                                    if (findAdditionalTemplate.length > 0 && findAdditionalTemplate.includes(template_id)) {
                                        findAdditionalTemplate.splice(findAdditionalTemplate.indexOf(template_id), 1)
                                        await UserMeta.update({ additional_template: findAdditionalTemplate.join(','), expire_date: expireDate, updated_by: req.user.id }, { where: { user_id: userMeta.user_id } })
                                    }


                                    await UserEmploymentContract.update({ contract_status: contract_status.INACTIVE, updated_by: req.user.id }, { where: { user_id: userMeta.user_id, contract_status: contract_status.ACTIVE } })

                                    await UserEmploymentContract.create({
                                        contract_with_sign: String(generatedPdf?.item_id),
                                        is_confirm_sign: false,
                                        contract_status: contract_status.ACTIVE,
                                        updated_by: req.user.id,
                                        created_by: req.user.id,
                                        general_template: generalContent?.id,
                                        department_template: deptContent?.id,
                                        additional_template: additionalLatestTemplateIds,
                                        tips_grade: userMeta?.tips_grade,
                                        other: userMeta?.other,
                                        expire_date: expireDate,
                                        user_joining_date: user?.user_joining_date,
                                        user_id: userMeta.user_id,
                                        fixed_types: userMeta?.fixed_types,
                                        working_hours: userMeta?.working_hours,
                                        duration_type: userMeta?.duration_type,
                                        wage_type: userMeta?.wage_type,
                                        contract_remark: userMeta?.contract_remark,
                                        contract_name: userMeta?.contract_name,
                                        leave_type_id: userMeta?.leave_type_id,
                                        leave_days: userMeta?.leave_days,
                                        leave_remark: userMeta?.leave_remark,
                                        leave_duration_type: userMeta?.leave_duration_type,
                                        place_of_work: userMeta?.place_of_work,
                                        working_hour_per_day: userMeta?.working_hour_per_day,
                                        max_limit_per_week: userMeta?.max_limit_per_week,
                                        contract_name_id: userMeta?.contract_name_id,
                                        has_holiday_entitlement: userMeta?.has_holiday_entitlement,
                                        holiday_entitlement_remark: userMeta?.holiday_entitlement_remark
                                    } as any)

                                    await UserCheckList.setHeaders(req).update({ status: check_list_status.PENDING }, { where: { to_user_id: userMeta.user_id, from_user_id: userMeta.user_id, checklist_id: 4 } });

                                    await User.update({ user_status: user_status.ONGOING }, { where: { id: userMeta.user_id } })

                                    const templateData = {
                                        name: user.user_first_name,
                                        admin_name: req.user?.user_first_name,
                                        email: user.user_email,
                                        mail_type: 'onboarding_regenerate',
                                        ORGANIZATION_LOGO: await getOrganizationLogo(req.user.organization_id),
                                        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
                                        ADDRESS: EMAIL_ADDRESS.ADDRESS,
                                        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                                        EMAIL: EMAIL_ADDRESS.EMAIL,
                                        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                                        smtpConfig: 'INFO'
                                    }
                                    await sendEmailNotification(templateData)

                                    await createNotification([user], req, NOTIFICATION_TYPE.INDIVIDUAL, NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.content(req.user.user_first_name, "regenerated"), NOTIFICATIONCONSTANT.ONBOARDING_FORM_REQUEST.heading, REDIRECTION_TYPE.ONBOARDING)
                                }
                            }
                        }
                    }
                }
            }
        }

        setTimeout(() => {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("CONTRACT_GENERATED_SUCCESSFULLY"),
            });
        }, 2000);
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

// Create or Update Contract Type
const createOrUpdateContractType = async (req: Request, res: Response) => {
    const { name, working_hours, duration_type, fixed_types, wage_per_hour, wage_type, remark, status } = req.body;
    const { id } = req.params;

    try {
        // Check if a record with the same name already exists
        const existingContractType = await ContractTypeModel.findOne({ where: { name }, raw: true });

        if (existingContractType && id && existingContractType.id !== Number(id)) {
            return res.status(StatusCodes.CONFLICT).json({ status: false, message: res.__("CONTRACT_TYPE_DUPLICATION_ERROR") });
        } else if (existingContractType && !id) {
            if (existingContractType.status != contract_status.ACTIVE) {
                const updateObj: any = {
                    name: name,
                    working_hours: working_hours,
                    duration_type: duration_type,
                    wage_per_hour: wage_per_hour,
                    wage_type: wage_type,
                    fixed_types: fixed_types,
                    remark: remark,
                    updated_by: req.user.id,
                    status: status ? status : contract_status.ACTIVE
                }
                await ContractTypeModel.setHeaders(req).update(updateObj, { where: { id: existingContractType.id } })
                return res.status(StatusCodes.CREATED).json({ status: true, message: res.__("CONTRACT_TYPE_CREATION_SUCCESS") });
            }
        }

        let contractType;

        if (id) {
            // Update existing contract type
            contractType = await ContractTypeModel.findByPk(id);
            if (!contractType) {
                return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__('CONTRACT_TYPE_NOT_FOUND') });
            }
            const updateObj: any = {
                name: name,
                working_hours: working_hours,
                duration_type: duration_type,
                wage_per_hour: wage_per_hour,
                wage_type: wage_type,
                fixed_types: fixed_types,
                remark: remark,
                updated_by: req.user.id,
                status: status ? status : contract_status.ACTIVE
            }
            await ContractTypeModel.setHeaders(req).update(updateObj, { where: { id: id } })
        } else {
            // Create new contract type
            contractType = await ContractTypeModel.setHeaders(req).create({
                name,
                working_hours,
                duration_type,
                wage_per_hour,
                wage_type,
                fixed_types,
                remark,
                status: status ? status : contract_status.ACTIVE,
                created_by: req.user.id,
                updated_by: req.user.id,
            } as any);
        }

        return res.status(StatusCodes.CREATED).json({ status: true, message: id ? res.__("CONTRACT_TYPE_UPDATION_SUCCESS") : res.__("CONTRACT_TYPE_CREATION_SUCCESS") });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

// Get List of Contract Types with Search and Filter
const getContractTypes = async (req: Request, res: Response) => {
    const { search, status, page, size, id } = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));
    try {
        const whereClause: any = {
            where: {
                // status: contract_status.ACTIVE
            },
        };

        if (page && size) {
            whereClause.limit = Number(limit)
            whereClause.offset = Number(offset)
        }

        if (id) {
            whereClause.where.id = id;
        }

        if (search) {
            whereClause.where.name = { [Op.like]: `%${search}%` };
        }

        if (status) {
            whereClause.where.status = status;
        }

        const contractTypes = await ContractTypeModel.findAndCountAll(whereClause);
        const response = getPaginatedItems(Number(size), Number(page), contractTypes?.count);

        const data = {
            data: await Promise.all(contractTypes.rows?.map(async (contract: any) => {
                if (contract.created_by) {
                    contract.created_by = await User.findOne({
                        attributes: ['id', 'user_status',
                            [
                                sequelize.literal(
                                    `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = User.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', User.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = User.user_avatar))
          END`
                                ),
                                "user_avatar_link",
                            ],
                            "user_avatar",
                            [sequelize.literal(`(User.user_avatar)`), "user_avatar_id"], [
                                sequelize.fn(
                                    "concat",
                                    sequelize.col("user_first_name"),
                                    " ",
                                    sequelize.col("user_last_name"),
                                ),
                                "user_full_name",
                            ]], where: { id: contract.created_by, organization_id: req.user.organization_id }
                    })
                }
                return contract
            })),
            ...response
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

// Delete Contract Type
const deleteContractType = async (req: Request, res: Response) => {
    const { id } = req.params;

    try {
        const contractType = await ContractTypeModel.findByPk(id);
        if (!contractType) {
            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__('CONTRACT_TYPE_NOT_FOUND') });
        }

        // // Check if the contract type is in use
        // const isInUse = await UserMeta.count({ where: { : id } });

        // if (isInUse) {
        //     return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__("CONTRACT_TYPE_DELETION_FAILED_IN_USE") });
        // }

        await ContractTypeModel.setHeaders(req).update({ status: contract_status.INACTIVE, updated_by: req.user.id }, { where: { id } });
        res.status(StatusCodes.OK).json({ status: true, message: res.__('CONTRACT_TYPE_DELETION_SUCCESS') });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


// Create or Update Job Role
const createOrUpdateJobRole = async (req: Request, res: Response) => {
    const { name, status } = req.body;
    const { id } = req.params;

    try {
        // Check if a record with the same name already exists
        const existingJobRole = await JobRoleModel.findOne({ where: { name }, raw: true });

        if (existingJobRole && id && existingJobRole.id !== Number(id)) {
            return res.status(StatusCodes.CONFLICT).json({ status: false, message: res.__('JOB_ROLE_DUPLICATION_ERROR') });
        } else if (existingJobRole && !id) {
            if (existingJobRole.status != jobStatus.ACTIVE) {
                await JobRoleModel.setHeaders(req).update({ status: jobStatus.ACTIVE, updated_by: req.user.id }, { where: { id: id } })
                return res.status(StatusCodes.CREATED).json({ status: true, message: res.__("JOB_ROLE_CREATION_SUCCESS"), data: existingJobRole });
            }
        }

        let jobRole;

        if (id) {
            // Update existing job role
            jobRole = await JobRoleModel.findByPk(id);
            if (!jobRole) {
                return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__('JOB_ROLE_NOT_FOUND') });
            }
            const updateObj: any = {
                name: name,
                updated_by: req.user.id,
                status: status ? status : jobStatus.ACTIVE
            }
            await JobRoleModel.setHeaders(req).update(updateObj, { where: { id: id } })
        } else {
            // Create new job role
            jobRole = await JobRoleModel.create({ name, status: status ? status : jobStatus.ACTIVE, created_by: req.user.id, updated_by: req.user.id } as any);
        }

        return res.status(StatusCodes.CREATED).json({ status: true, message: id ? res.__("JOB_ROLE_UPDATION_SUCCESS") : res.__("JOB_ROLE_CREATION_SUCCESS"), data: jobRole });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

// Get List of Job Roles with Search and Filter
const getJobRoles = async (req: Request, res: Response) => {
    const { search, status, page = 1, size = 10, id } = req.query;
    const { limit, offset } = getPagination(Number(page), Number(size));

    try {
        const whereClause: any = {
            where: {
                // status: jobStatus.ACTIVE
            },
            limit, offset
        };
        if (id) {
            whereClause.where.id = id;
        }
        if (search) {
            whereClause.where.name = { [Op.like]: `%${search}%` };
        }

        if (status) {
            whereClause.where.status = status;
        }

        const jobRoles = await JobRoleModel.findAndCountAll(whereClause);
        const response = getPaginatedItems(Number(size), Number(page), jobRoles?.count);

        const data = {
            data: await Promise.all(jobRoles.rows?.map(async (job: any) => {
                if (job.created_by) {
                    job.created_by = await User.findOne({
                        attributes: ['id', 'user_status',
                            [
                                sequelize.literal(
                                    `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = User.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', User.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = User.user_avatar))
          END`
                                ),
                                "user_avatar_link",
                            ],
                            "user_avatar",
                            [sequelize.literal(`(User.user_avatar)`), "user_avatar_id"], [
                                sequelize.fn(
                                    "concat",
                                    sequelize.col("user_first_name"),
                                    " ",
                                    sequelize.col("user_last_name"),
                                ),
                                "user_full_name",
                            ]], where: { id: job.created_by }
                    })
                }
                return job
            })),
            ...response
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

// Delete Job Role
const deleteJobRole = async (req: Request, res: Response) => {
    const { id } = req.params;

    try {
        const jobRole = await JobRoleModel.findByPk(id);
        if (!jobRole) {
            return res.status(StatusCodes.NOT_FOUND).json({ status: false, message: res.__('JOB_ROLE_NOT_FOUND') });
        }

        // Check if the job role is in use
        const isInUse = await User.count({ where: { user_designation: jobRole.id } });

        if (isInUse) {
            return res.status(StatusCodes.BAD_REQUEST).json({ status: false, message: res.__('JOB_ROLE_DELETION_FAILED_IN_USE') });
        }

        await JobRoleModel.setHeaders(req).update({ status: jobStatus.INACTIVE, updated_by: req.user.id }, { where: { id: id } })
        res.status(StatusCodes.OK).json({ status: true, message: res.__('JOB_ROLE_DELETION_SUCCESS') });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};



// update user contract
const updateUserContract = async (req: Request, res: Response) => {
    try {
        const { general_template = 0,
            department_template = null,
            expire_date = "",
            other = "",
            expire_duration = "",
            wages_hours = 0,
            leave_policy_id = 0, user_id,
            additional_template = "",
            tips_grade = null,
            fixed_types,
            probation_length = 0,
            working_hours,
            duration_type,
            wage_type,
            contract_remark,
            contract_name,
            leave_type_id,
            leave_days,
            leave_remark,
            leave_duration_type,
            place_of_work,
            contract_name_id,
            has_holiday_entitlement,
            holiday_entitlement_remark,
            leave_policy_ids
        } = req.body
        const policyIds = leave_policy_ids ? leave_policy_ids.split(",").map(Number) : []
        const findUser = await User.findOne({ attributes: ['id', 'user_joining_date'], where: { id: user_id, organization_id: req.user.organization_id }, raw: true })
        if (!findUser) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("USER_NOT_EXIST") });
        }
        const findGeneralSetting: any = await getGeneralSettingObj(req.user.organization_id)
        const createUserMeta: any = {
            user_id: findUser.id,
            general_template: null,
            department_template: null,
            additional_template: null,
            expire_date: expire_date ? moment(expire_date) : null,
            other,
            created_by: req.user.id,
            isDraft: true,
            expire_duration,
            wages_hours: wages_hours ? wages_hours : null,
            leave_policy_id,
            tips_grade,
            fixed_types: fixed_types ? fixed_types : null,
            probation_length: probation_length ? Number(probation_length) : null,
            working_hours,
            duration_type,
            wage_type,
            contract_remark,
            contract_name,
            leave_type_id,
            leave_days,
            leave_remark,
            leave_duration_type,
            place_of_work,
            working_hour_per_day: findGeneralSetting?.working_hours_per_day ? findGeneralSetting?.working_hours_per_day : null,
            max_limit_per_week: findGeneralSetting?.max_limit_per_week ? findGeneralSetting?.max_limit_per_week : null,
            contract_name_id,
            has_holiday_entitlement,
            holiday_entitlement_remark
        }
        if (general_template) {
            const findGeneralTemplate = await EmpContractTemplate.findOne({ attributes: ['id'], where: { id: general_template }, raw: true })
            if (findGeneralTemplate) {
                createUserMeta.general_template = findGeneralTemplate.id
            }
        }
        if (department_template) {
            const findDeptTemplate = await EmpContractTemplate.findOne({ attributes: ['id'], where: { id: department_template }, raw: true })
            if (findDeptTemplate) {
                createUserMeta.department_template = findDeptTemplate.id
            }
        }
        if (additional_template) {
            const findAdditionalTemplate = additional_template.split(",").map(Number)
            const findLatestAdditionalArr = []
            if (findAdditionalTemplate.length > 0) {
                for (const empContract of findAdditionalTemplate) {
                    const findContractTemplate = await EmpContractTemplate.findOne({ attributes: ['id'], where: { id: empContract }, raw: true })
                    findLatestAdditionalArr.push(findContractTemplate?.id)
                }
                createUserMeta.additional_template = findLatestAdditionalArr.toString();
            }
        }


        const findUserMeta = await UserMeta.findOne({ where: { user_id: findUser.id }, raw: true });
        if (findUserMeta) {
            createUserMeta.updated_by = req.user.id
            await UserMeta.update(createUserMeta, { where: { user_id: findUser.id } })

        } else {
            await UserMeta.create(createUserMeta)
        }
        if (policyIds.length > 0) {
            for (const policy of policyIds) {
                const checkPolicyExist = await LeaveAccuralPolicy.findOne({
                    attributes: ['id', 'leave_policy_end_date', 'leave_calender_year_start_from', 'stop_policy_accural_timewise_type', 'stop_policy_accural_timewise_value', 'leave_balance_based_on_emp_contract', 'leave_policy_accural', 'effective_from_type', 'effective_after_type', 'effective_after_count'], raw: true, nest: true,
                    where: { id: policy, status: status.ACTIVE }
                });

                if (checkPolicyExist) {
                    const findLeaveYear = checkPolicyExist.leave_calender_year_start_from;
                    const findLeaveType = checkPolicyExist.stop_policy_accural_timewise_type || 'yearly';
                    const findLeaveObj = checkPolicyExist.stop_policy_accural_timewise_value ? JSON.parse(checkPolicyExist.stop_policy_accural_timewise_value)
                        : checkPolicyExist.stop_policy_accural_timewise_value;
                    const leaveBalanceBasedOnEmpContract = checkPolicyExist.leave_balance_based_on_emp_contract
                    const findLeaveEnd = checkPolicyExist.leave_policy_end_date;
                    // Calculate the probation end date
                    const probationEndDate = moment(findUser?.user_joining_date).clone().add(probation_length, "days");
                    const isUserOnProbation = moment().isSameOrBefore(moment(probationEndDate), 'day');

                    let baseDate;

                    // Determine base date based on effective_from_type
                    if (checkPolicyExist.effective_from_type == 'date_of_joining') {
                        baseDate = moment(findUser?.user_joining_date);
                    } else if (checkPolicyExist.effective_from_type == 'after_probation_end' && isUserOnProbation) {
                        baseDate = moment(probationEndDate);
                    }

                    // Calculate the effective date
                    const effectiveDate = moment(baseDate);
                    if (checkPolicyExist.effective_after_type === 'days') {
                        effectiveDate.add(checkPolicyExist.effective_after_count, 'days');
                    } else if (checkPolicyExist.effective_after_type === 'months') {
                        effectiveDate.add(checkPolicyExist.effective_after_count, 'months');
                    }


                    const findExistingPolicy = await UserLeavePolicy.findOne({
                        where: {
                            leave_accural_policy_id: policy, // Exclude policy_id
                            user_id: findUser.id,
                        }, raw: true
                    })
                    const currentYear = moment().year();
                    const generalSettings = await getGeneralSettingObj(req.user.organization_id)
                    if (findExistingPolicy) {
                        // Store user I
                        await UserLeavePolicy.update(
                            { user_leave_policy_status: user_leave_policy_status.ACTIVE },
                            {
                                where: {
                                    id: findExistingPolicy.id
                                }
                            }
                        );

                        if (checkPolicyExist.leave_policy_accural) {
                            const findUserMeta = await UserMeta.findOne({ attributes: ['leave_days', 'leave_type_id'], where: { user_id: findUser.id }, raw: true });
                            await handleLeaveAccrual(findLeaveType, findLeaveObj, findExistingPolicy, findUserMeta, generalSettings, currentYear, req, findUser, leaveBalanceBasedOnEmpContract, findLeaveYear, findLeaveEnd)
                        }

                    } else {
                        const userLeavePolicyCreated = await UserLeavePolicy.create({
                            user_id: findUser?.id,
                            leave_accural_policy_id: policy,
                            user_leave_policy_status: user_leave_policy_status.ACTIVE,
                            created_by: req.user.id,
                            updated_by: req.user.id
                        } as any)
                        if (userLeavePolicyCreated) {
                            if (checkPolicyExist.leave_policy_accural) {
                                const findUserMeta = await UserMeta.findOne({ attributes: ['leave_days', 'leave_type_id'], where: { user_id: findUser.id }, raw: true });
                                await handleLeaveAccrual(findLeaveType, findLeaveObj, userLeavePolicyCreated, findUserMeta, generalSettings, currentYear, req, findUser, leaveBalanceBasedOnEmpContract, findLeaveYear, findLeaveEnd)
                            }
                        }
                    }

                }

            }
            await UserLeavePolicy.update(
                { user_leave_policy_status: user_leave_policy_status.INACTIVE },
                {
                    where: {
                        leave_accural_policy_id: { [Op.notIn]: policyIds }, // Exclude policy_id
                        user_id: findUser.id,
                    },
                }
            )
        } else {
            await UserLeavePolicy.update(
                { user_leave_policy_status: user_leave_policy_status.INACTIVE },
                {
                    where: {
                        user_id: findUser.id,
                    },
                }
            );
        }
        return res
            .status(StatusCodes.OK)
            .json({ status: true, message: res.__("USER_UPDATION_SUCCESSED") });

    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

export const fetchUserLeavePolicies = async (user_id: string, leave_type_id?: any, duration_type?: string) => {
    try {
        // Fetch user meta information to get leave_days and leave_type_id
        const findEmpContract = await UserMeta.findOne({
            attributes: ['leave_type_id', 'leave_days'],
            where: { user_id: user_id },
        });

        // Fetch all active leave types or filter by leave_type_id if it's provided
        const leaveTypeQuery: any = { status: status.ACTIVE };
        if (leave_type_id) {
            leaveTypeQuery.id = leave_type_id;
        }

        const activeLeaveTypes: any = await LeaveTypeModel.findAll({
            where: leaveTypeQuery,
            raw: true,
        });

        // Prepare an array to store the results
        const results = [];

        // Use a normal for loop to iterate over the active leave types
        for (const type of activeLeaveTypes) {
            // Check if leave_type_id in UserMeta matches the current leave type
            if (findEmpContract && type.id === findEmpContract.leave_type_id) {
                // Fetch the total leave days taken by the user for this leave type
                const findUserExistingLeaveCount: any = await UserRequest.findAll({
                    where: {
                        leave_request_type: type.id,
                        from_user_id: user_id,
                        request_status: { [Op.not]: request_status.REJECTED },
                    },
                    attributes: [[Sequelize.fn('SUM', Sequelize.col('leave_days')), 'total_days']],
                    raw: true,
                });

                const totalLeaveDays = findUserExistingLeaveCount[0]?.total_days || 0;
                let pending_balance = findEmpContract.leave_days - totalLeaveDays;

                // If the balance is negative, set it to 0
                if (pending_balance < 0) {
                    pending_balance = 0;
                }

                // Push the result for the matching leave type
                results.push({
                    id: type.id,
                    type_name: type.name,  // Include type_name here
                    balance: parseFloat(pending_balance.toFixed(1)),
                    total_leaves: findEmpContract.leave_days,
                    has_unlimited: 0,  // Only matching leave types have limited leaves
                    duration_type: duration_type
                });
            } else {
                // Push default values for non-matching leave types
                results.push({
                    id: type.id,
                    type_name: type.name,
                    balance: 0,
                    total_leaves: 0,
                    has_unlimited: 1,  // Non-matching types have unlimited leaves
                    duration_type: duration_type
                });
            }
        }

        return results;  // Return the array of results
    } catch (error) {
        console.error(error);
        throw new Error('Error fetching leave policies');
    }
};


const getUserLeavePolicies = async (req: Request, res: Response) => {
    try {
        const { user_id } = req.params;
        const { duration_type }: any = req.query;
        const leavePolicies = await fetchUserLeavePolicies(user_id, null, duration_type);

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__('SUCCESS_FETCHED'),
            data: leavePolicies || [],
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__('SOMETHING_WENT_WRONG'),
            data: error,
        });
    }
};

const createContractName = async (req: Request, res: Response) => {
    try {
        const { contract_name }: any = req.body;
        const findContract = await ContractNameModel.findOne({ attributes: ['id'], where: { contract_name, organization_id: req.user.organization_id }, raw: true });
        if (findContract) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__('CONTRACT_NAME_DUPLICATION_ERROR') });
        }

        const createContract = await ContractNameModel.create({ contract_name, organization_id: req.user.organization_id, created_by: req.user.id, updated_by: req.user.id } as any);
        if (createContract) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__('CONTRACT_CREATED_SUCCESSFULLY')
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__('FAIL_TO_CREATE_CONTRACT')
            });
        }

    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__('SOMETHING_WENT_WRONG'),
            data: error,
        });
    }
};

const getContractName = async (req: Request, res: Response) => {
    try {

        const findContract = await ContractNameModel.findAll({
            where: { organization_id: req.user.organization_id }, raw: true
        });
        return res.status(StatusCodes.OK).json({
            status: true,
            data: findContract.length > 0 ? findContract : [],
            message: res.__("SUCCESS_FETCHED")
        });

    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__('SOMETHING_WENT_WRONG'),
            data: error,
        });
    }
};

const deleteUserContractHistory = async (req: Request, res: Response) => {
    try {
        const getUserDetail: any = await User.findOne({
            attributes: ['id', 'web_user_active_role_id', 'user_active_role_id'],
            where: { id: req.user.id, organization_id: req.user.organization_id }, raw: true
        });
        if (!getUserDetail) {
            return res
                .status(StatusCodes.EXPECTATION_FAILED)
                .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
        }

        const findUserRole: any = await Role.findOne({
            attributes: ['role_name'],
            where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id }, raw: true
        });

        if (![ROLE_CONSTANT.SUPER_ADMIN, ROLE_CONSTANT.ADMIN].includes(findUserRole?.role_name)) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("PERMISSION_DENIED"),
            });
        }
        const contract_ids = req.query.contract_ids ? `${req.query.contract_ids}`.split(",").map(Number) : []
        if (contract_ids.length > 0) {
            await UserEmploymentContract.update({ contract_status: contract_status.DELETED }, { where: { id: { [Op.in]: contract_ids }, contract_status: contract_status.INACTIVE } })
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__('EMP_CONTRACT_HISTROY_DELETED_SUCCESSFULLY'),
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__('SOMETHING_WENT_WRONG'),
            data: error,
        });
    }
};

const getUsersByTemplateGroups = async (req: Request, res: Response) => {
    try {
        const { general_user_id = [], department_user_id = [], additional_user_id = [] } = req.body;

        // Combine all IDs to fetch users in a single query
        const allUserIds = [...new Set([...general_user_id, ...department_user_id, ...additional_user_id])];

        if (allUserIds.length === 0) {
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("NO_USERS_FOUND"),
                data: {
                    general_user_id: [],
                    department_user_id: [],
                    additional_user_id: []
                }
            });
        }

        // Fetch all users in one query
        const users = await User.findAll({
            where: {
                id: { [Op.in]: allUserIds },
                user_status: { [Op.in]: [user_status.ACTIVE, user_status.ONGOING, user_status.VERIFIED, user_status.COMPLETED] },
                organization_id: req.user.organization_id
            },
            attributes: [
                'id',
                [sequelize.fn(
                    "concat",
                    sequelize.col("user_first_name"),
                    " ",
                    sequelize.col("user_last_name"),
                ), "user_full_name"],
                'user_email',
                [sequelize.literal(
                    `CASE 
            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
          END`
                ), "user_avatar"],
                'user_status',
                'employment_number'
            ],
            raw: true
        });

        // Create a map of all found users
        const userMap = new Map(users.map(user => [user.id, user]));

        // Prepare the response object with valid users
        const validGeneral = general_user_id.filter((id: number) => userMap.has(id)).map((id: number) => userMap.get(id));
        const validDepartment = department_user_id.filter((id: number) => userMap.has(id)).map((id: number) => userMap.get(id));
        const validAdditional = additional_user_id.filter((id: number) => userMap.has(id)).map((id: number) => userMap.get(id));

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: {
                general_user_id: validGeneral,
                department_user_id: validDepartment,
                additional_user_id: validAdditional
            }
        });
    } catch (error) {
        console.error(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};

export default {
    createOrUpdateFile,
    copyInDuplicateFile,
    moveFileToCategory,
    getFolders,
    getAllFiles,
    getAllFileVersions,
    deleteFilesFolder,
    getAllUserContractVersion,
    regenerateContractAfterUpdate,
    createOrUpdateContractType,
    getContractTypes,
    deleteContractType,
    createOrUpdateJobRole,
    getJobRoles,
    deleteJobRole,
    updateUserContract,
    getUserLeavePolicies,
    createContractName,
    getContractName,
    deleteUserContractHistory,
    getUsersByTemplateGroups
}

