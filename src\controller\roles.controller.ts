import { Request, Response } from "express";
import { Role, role_status } from '../models/MORole';
import { Op, Transaction } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
import { StatusCodes } from "http-status-codes";
import { ROLE_PERMISSIONS } from "../helper/constant";
import { MOPermission, status as permission_status } from "../models/MOPermission";
import { MOModule } from "../models/MOModule";
import { MOWidget, widget_type, widget_sub_type } from "../models/MOWidget";
import { User } from "../models/User";
import { sequelize } from "../models";
import _ from 'lodash'
import { validateModulePermission } from "../helper/common";
import { updateIndexesAPI } from "../scripts/update-indexes";
import { createDefaultWidgetsAndPermissions } from "../services/auth.service";

// Create Role
export const createRole = async (req: Request, res: Response) => {
    try {
        const { role_name, parent_role_id, platform, additional_permissions } = req.body;

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.CREATE
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [ROLE_CONSTANT.SUPER_ADMIN]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        // Duplicate validation
        const existingRole = await Role.findOne({
            where: {
                role_name,
                organization_id: req.user.organization_id
            }
        });
        if (existingRole) {
            if (existingRole.role_status === role_status.ACTIVE) {
                return res.status(400).json({
                    status: false,
                    message: res.__("ROLE_ALREADY_EXISTS"),
                });
            } else {
                await Role.setHeaders(req).update(
                    { role_status: role_status.ACTIVE },
                    {
                        where: {
                            id: existingRole.id,
                            organization_id: req.user.organization_id
                        }
                    }
                )
                return res.status(200).json({
                    status: true,
                    message: res.__("ROLE_UPDATED_SUCCESSFULLY")
                });
            }
        }

        const role = await Role.setHeaders(req).create({
            role_name,
            parent_role_id,
            platform,
            additional_permissions: additional_permissions ? JSON.stringify(additional_permissions) : null,
            created_by: req.user.id,
            updated_by: req.user.id,
            role_status: role_status.ACTIVE,
            organization_id: req.user.organization_id
        } as any);

        // Automatically assign 'self' category widget permissions to new role
        try {
            await handleWidgetCategoryPermissions(role.id, ['self'], req.user.organization_id, req.user.id);
            console.log(`✅ Assigned 'self' category widget permissions to new role: ${role_name} (ID: ${role.id})`);
        } catch (error) {
            console.error(`⚠️ Failed to assign default widget permissions to role ${role.id}:`, error);
            // Don't fail the role creation if widget permission assignment fails
        }

        return res.status(201).json({
            status: true,
            message: res.__("ROLE_CREATED_SUCCESSFULLY"),
            data: role
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Roles
export const getRoles = async (req: Request, res: Response) => {
    try {
        const { search, status, page, size, id } = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.VIEW
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [ROLE_CONSTANT.SUPER_ADMIN]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const whereClause: any = {
            where: {
                [Op.or]: [
                    { organization_id: req.user.organization_id }
                ]
            },
            order: [['index', 'ASC']], // Order by index for organization-scoped ordering
            raw: true
        };

        if (page && size) {
            whereClause.limit = limit;
            whereClause.offset = offset;
        }

        if (id) {
            whereClause.where.id = id;
        }

        if (search) {
            whereClause.where.role_name = { [Op.like]: `%${search}%` };
        }

        if (status) {
            whereClause.where.role_status = status;
        }

        const Roles = await Role.findAndCountAll(whereClause);

        // Get unique created_by user IDs
        const createdByUserIds = [...new Set(Roles.rows.map((role: any) => role.created_by).filter(Boolean))];

        // Fetch user details for created_by users
        const users = await User.findAll({
            where: { id: { [Op.in]: createdByUserIds } },
            attributes: [
                "id",
                "user_first_name",
                "user_last_name",
                [
                    sequelize.fn(
                        "concat",
                        sequelize.col("user_first_name"),
                        " ",
                        sequelize.col("user_last_name")
                    ),
                    "user_full_name",
                ],
                [
                    sequelize.literal(
                        `CASE
                        WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
                        WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar)
                        THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
                        ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
                        END`
                    ),
                    "user_avatar",
                ],
            ],
            raw: true
        });

        // Create user map for quick lookup
        const userMap = new Map(users.map(user => [user.id, user]));

        // Get widget categories for each role
        const roleIds = Roles.rows.map((role: any) => role.id);
        const widgetCategoriesMap = new Map();

        if (roleIds.length > 0) {
            // Get widget permissions for all roles
            const widgetPermissions = await MOPermission.findAll({
                where: {
                    role_id: { [Op.in]: roleIds },
                    organization_id: req.user.organization_id,
                    widget_id: { [Op.not]: null }
                } as any,
                attributes: ['role_id', 'widget_id'],
                raw: true
            });

            // Get widget IDs that have permissions
            const widgetIds = [...new Set(widgetPermissions.map((perm: any) => perm.widget_id))];

            if (widgetIds.length > 0) {
                // Get widget categories
                const widgets = await MOWidget.findAll({
                    where: {
                        id: { [Op.in]: widgetIds },
                        organization_id: req.user.organization_id
                    },
                    attributes: ['id', 'widget_category'],
                    raw: true
                });

                // Create widget category map
                const widgetCategoryMap = new Map(widgets.map((widget: any) => [widget.id, widget.widget_category]));

                // Group categories by role
                widgetPermissions.forEach((perm: any) => {
                    const category = widgetCategoryMap.get(perm.widget_id);
                    if (category) {
                        if (!widgetCategoriesMap.has(perm.role_id)) {
                            widgetCategoriesMap.set(perm.role_id, new Set());
                        }
                        widgetCategoriesMap.get(perm.role_id).add(category);
                    }
                });
            }
        }

        // Add created_by user details and widget categories to roles
        const rolesWithUserDetails = Roles.rows.map((role: any) => {
            const widgetCategoriesSet = widgetCategoriesMap.get(role.id) || new Set();
            return {
                ...role,
                created_by: role.created_by ? userMap.get(role.created_by) || null : null,
                widget_categories: Array.from(widgetCategoriesSet).sort() // Convert Set to sorted Array
            };
        });

        const response = getPaginatedItems(Number(size), Number(page), Roles?.count);

        const data = {
            data: rolesWithUserDetails,
            ...response
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Helper function to handle widget category permissions
const handleWidgetCategoryPermissions = async (
    roleId: number,
    widgetCategories: string[],
    organizationId: string,
    userId: number
) => {
    try {
        // Get all existing widget permissions for this role
        const existingPermissions = await MOPermission.findAll({
            where: {
                role_id: roleId,
                organization_id: organizationId,
                widget_id: { [Op.not]: null }
            } as any,
            attributes: ['id', 'widget_id']
        });

        // Get all widgets for the specified categories in this organization
        const categoryWidgets = await MOWidget.findAll({
            where: {
                organization_id: organizationId,
                widget_category: { [Op.in]: widgetCategories }
            },
            attributes: ['id', 'widget_category']
        });

        // Get all widgets in this organization to check categories for removal
        const allOrgWidgets = await MOWidget.findAll({
            where: {
                organization_id: organizationId
            },
            attributes: ['id', 'widget_category']
        });

        // Create widget category map
        const widgetCategoryMap = new Map();
        allOrgWidgets.forEach(w => widgetCategoryMap.set(w.id, w.widget_category));

        // Create a set of widget IDs that currently have permissions
        const currentWidgetIds = new Set(existingPermissions.map(p => p.widget_id));

        // Add permissions for new widgets (widgets in target categories that don't have permissions)
        const widgetsToAdd = categoryWidgets.filter(w => !currentWidgetIds.has(w.id));
        for (const widget of widgetsToAdd) {
            await MOPermission.create({
                role_id: roleId,
                widget_id: widget.id,
                organization_id: organizationId,
                permission: 1, // View permission
                order: 0,
                created_by: userId,
                updated_by: userId
            } as any);
        }

        // Remove permissions for widgets not in target categories
        const widgetsToRemove = existingPermissions.filter(p => {
            const widgetCategory = widgetCategoryMap.get(p.widget_id);
            return widgetCategory && !widgetCategories.includes(widgetCategory);
        });

        if (widgetsToRemove.length > 0) {
            await MOPermission.destroy({
                where: {
                    id: { [Op.in]: widgetsToRemove.map(p => p.id) }
                }
            });
        }

        console.log(`Widget permissions updated for role ${roleId}:`);
        console.log(`- Added permissions for ${widgetsToAdd.length} widgets`);
        console.log(`- Removed permissions for ${widgetsToRemove.length} widgets`);

    } catch (error) {
        console.error('Error handling widget category permissions:', error);
        throw error;
    }
};

// Update Role
export const updateRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { role_name, parent_role_id, platform, additional_permissions, widget_categories } = req.body;

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.EDIT
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [ROLE_CONSTANT.SUPER_ADMIN]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;

        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        if (role_name) {
            // Duplicate validation
            const existingRole = await Role.findOne({
                where: {
                    role_name,
                    id: { [Op.ne]: id },
                    organization_id: req.user.organization_id
                }
            });
            if (existingRole && existingRole.role_status != role_status.INACTIVE) {
                return res.status(400).json({
                    status: false,
                    message: res.__("ROLE_ALREADY_EXISTS"),
                });
            }
        }

        const role = await Role.findOne({
            where: {
                id,
                organization_id: req.user.organization_id
            }
        });
        if (!role) {
            return res.status(404).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND"),
            });
        }

        if(parent_role_id && parent_role_id > id){
            return res.status(400).json({
                status: false,
                message: res.__("PARENT_ROLE_CANNOT_BE_CHILD")
            });
        }

        const updateObj: any = {
            role_name: role_name ? role_name : role.role_name,
            parent_role_id: parent_role_id ? parent_role_id : role.parent_role_id,
            platform: platform ? platform : role.platform,
            additional_permissions: additional_permissions !== undefined ?
                (additional_permissions ? JSON.stringify(additional_permissions) : null) :
                role.additional_permissions,
            updated_by: req.user.id,
            role_status: role_status.ACTIVE,
        }

        await Role.setHeaders(req).update(updateObj, {
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        })

        // Handle widget categories if provided
        if (widget_categories && Array.isArray(widget_categories)) {
            await handleWidgetCategoryPermissions(role.id, widget_categories, req.user.organization_id, req.user.id);
        }

        return res.status(200).json({
            status: true,
            message: res.__("ROLE_UPDATED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Soft Delete Role (Set status to inactive)
export const deleteRole = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.DELETE
        );

        // Enhanced admin permission check (combines both old and new systems)
        // const checkAdminPermission = await permittedForAdminEnhanced(
        //     req.user?.id,
        //     req.user.organization_id,
        //     [ROLE_CONSTANT.SUPER_ADMIN]
        // );

        // User has permission if either check passes
        const hasPermission = checkModulePermission; // || checkAdminPermission;
        if (!hasPermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }


        const role = await Role.findOne({
            where: {
                id,
                organization_id: req.user.organization_id
            }
        });
        if (!role) {
            return res.status(404).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND"),
            });
        }

        await Role.setHeaders(req).update(
            { role_status: role_status.INACTIVE },
            {
                where: {
                    id: id,
                    organization_id: req.user.organization_id
                }
            }
        )

        return res.status(200).json({
            status: true,
            message: res.__("ROLE_SET_INACTIVE_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


// Ensure view permission is added if create/edit/delete is present
export const setViewPermission = (permission: number) => {
    if (permission & (ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT | ROLE_PERMISSIONS.DELETE)) {
        permission |= ROLE_PERMISSIONS.VIEW;  // Add view permission if create/edit/delete is present
    }
    return permission;
};

// Format permission response
export const formatPermissions = (permissions: any) => {
    const result: any = {};

    permissions.sort((a: any, b: any) => a?.module?.index - b?.module?.index).forEach((perm: any) => {
        const permObj = {
            none: perm.permission == 0,
            view: (perm.permission & ROLE_PERMISSIONS.VIEW) > 0,
            create: (perm.permission & ROLE_PERMISSIONS.CREATE) > 0,
            edit: (perm.permission & ROLE_PERMISSIONS.EDIT) > 0,
            delete: (perm.permission & ROLE_PERMISSIONS.DELETE) > 0,
            partial: perm.partial ? true : false,
            module_name: perm?.module?.module_name,
            index: perm?.module?.index
        };

        result[perm.module.module] = permObj;
    });

    return result;
};

export const createPermission = async (req: Request, res: Response) => {
    const { role_id, module_ids, permission, partial, platform, additional_permissions } = req.body; // Changed module_id to module_ids

    try {
        const createdPermissions = [];

        for (const module_id of module_ids) { // Loop through module_ids
            const existingPermission = await MOPermission.findOne({
                where: {
                    role_id,
                    module_id,
                    organization_id: req.user.organization_id
                }
            });

            if (existingPermission && existingPermission.status == permission_status.ACTIVE) {
                // Skip if permission already exists for this module
                continue;
            }

            // Ensure view permission is added if create/edit/delete is present
            const finalPermission = setViewPermission(permission);

            const newPermission = await MOPermission.setHeaders(req).create({
                role_id,
                module_id,
                permission: finalPermission,
                partial,
                created_by: req.user.id,
                updated_by: req.user.id,
                organization_id: req.user.organization_id
            } as any);

            createdPermissions.push(newPermission);

            // Propagate permissions to other roles in the organization for this module
            const Roles = await Role.findAll({
                where: {
                    id: { [Op.ne]: role_id },
                    organization_id: req.user.organization_id
                }
            });

            if (Roles.length) {
                await Promise.all(Roles.map(async (role) => {
                    const findRolePermission = await MOPermission.findOne({
                        where: {
                            role_id: role.id,
                            module_id: module_id,
                            organization_id: req.user.organization_id
                        }
                    });
                    if (findRolePermission) {
                        await MOPermission.setHeaders(req).update(
                            { permission: findRolePermission.permission },
                            {
                                where: {
                                    role_id: role.id,
                                    module_id: module_id,
                                    organization_id: req.user.organization_id
                                }
                            }
                        )
                    } else {
                        await MOPermission.setHeaders(req).create({
                            role_id: role.id,
                            module_id,
                            permission: 0,
                            partial,
                            created_by: req.user.id,
                            updated_by: req.user.id,
                            organization_id: req.user.organization_id
                        } as any)
                    }
                }))
            }
        }
        const updateObj: any = { platform: platform }
        if (additional_permissions) {
            updateObj.additional_permissions = JSON.stringify(additional_permissions)
        }
        await Role.setHeaders(req).update(
            updateObj,
            {
                where: {
                    id: role_id,
                    organization_id: req.user.organization_id
                }
            }
        )
        // Platform and additional_permissions are now handled in role APIs
        if (createdPermissions.length === 0) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS_FOR_ROLE")
            });
        }

        return res.status(201).json({
            status: true,
            message: res.__("PERMISSION_CREATED_SUCCESSFULLY"),
            data: createdPermissions
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

export const updatePermission = async (req: Request, res: Response) => {
    const { roles } = req.body; // Array of roles with their module permissions

    try {
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'permission', // Role module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.['platform-type'] as string
        );

        if (!checkModulePermission) {
            return res.status(403).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        const permissionUpdates: any[] = [];
        const permissionCreates: any[] = [];
        const invalidRoles: number[] = [];
        const invalidModules: any[] = [];

        // Validate all roles exist first
        const roleIds = roles.map((r: any) => r.role_id).filter((id: any) => id);
        const existingRoles = await Role.findAll({
            where: {
                id: { [Op.in]: roleIds },
                organization_id: req.user.organization_id
            },
            attributes: ['id'],
            raw: true
        });

        const existingRoleIds = existingRoles.map(r => r.id);
        const missingRoleIds = roleIds.filter((id: any) => !existingRoleIds.includes(id));

        if (missingRoleIds.length > 0) {
            return res.status(400).json({
                status: false,
                message: res.__("SOME_TO_ROLES_NOT_FOUND"),
                missing_role_ids: missingRoleIds
            });
        }

        // Process each role and its modules
        for (const roleData of roles) {
            const { role_id, modules } = roleData;

            // Get all existing permissions for this role
            const existingPermissions = await MOPermission.findAll({
                where: {
                    role_id: role_id,
                    organization_id: req.user.organization_id
                },
                raw: true
            });

            // Process each module permission for this role
            for (const moduleData of modules) {
                const { module_id, permission, partial } = moduleData;

                // Find existing permission using filter
                const existingPerm = existingPermissions.find(perm => perm.module_id === module_id);
                const finalPermission = setViewPermission(permission);

                // Handle partial field properly - convert null to false, keep boolean values
                const partialValue = partial === null || partial === undefined ? false : Boolean(partial);


                if (existingPerm) {
                    // Update existing permission
                    permissionUpdates.push({
                        updateData: {
                            permission: finalPermission ? finalPermission : partialValue ? 1 : finalPermission,
                            partial: partialValue,
                            status: permission_status.ACTIVE,
                            updated_by: req.user.id
                        },
                        whereClause: {
                            id: existingPerm.id,
                            organization_id: req.user.organization_id
                        }
                    });
                } else {
                    // Create new permission
                    permissionCreates.push({
                        role_id: role_id,
                        module_id: module_id,
                        organization_id: req.user.organization_id,
                        permission: finalPermission ? finalPermission : partialValue ? 1 : finalPermission,
                        partial: partialValue,
                        status: permission_status.ACTIVE,
                        created_by: req.user.id,
                        updated_by: req.user.id
                    });
                }
            }
        }

        // Report validation errors if any
        if (invalidRoles.length > 0 || invalidModules.length > 0) {
            return res.status(400).json({
                status: false,
                message: res.__("INVALID_ROLES_DATA"),
                errors: {
                    invalid_roles: invalidRoles,
                    invalid_modules: invalidModules
                }
            });
        }

        // Process updates sequentially without transaction
        let updatedCount = 0;
        let createdCount = 0;

        // Process updates sequentially
        for (const update of permissionUpdates) {
            try {
                const [affectedRows] = await MOPermission.setHeaders(req).update(
                    update.updateData,
                    {
                        where: update.whereClause
                    }
                );
                if (affectedRows > 0) {
                    updatedCount++;
                }
            } catch (error) {
                console.log('Error updating permission:', error, update);
                // Continue with next update instead of stopping
            }
        }

        // Process creates sequentially
        for (const createData of permissionCreates) {
            try {
                await MOPermission.setHeaders(req).create(createData as any);
                createdCount++;
            } catch (error) {
                console.log('Error creating permission:', error, createData);
                // Continue with next create instead of stopping
            }
        }

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_UPDATED_SUCCESSFULLY"),
            data: {
                updated: updatedCount,
                created: createdCount,
                total_processed: updatedCount + createdCount,
                roles_processed: roles.length
            }
        });

    } catch (error) {
        console.log('Error in updatePermission:', error);
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

export const updateWidgetPermission = async (req: Request, res: Response) => {
    const { role_id, widgets } = req.body;

    try {
        // Validate input
        if (!role_id || !widgets || !Array.isArray(widgets)) {
            return res.status(400).json({
                status: false,
                message: res.__("INVALID_INPUT_DATA")
            });
        }

        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'permission', // Permission module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res.status(403).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        // Validate that the role exists and belongs to the organization
        const roleExists = await Role.findOne({
            where: {
                id: role_id,
                organization_id: req.user.organization_id,
                role_status: 'active'
            }
        });

        if (!roleExists) {
            return res.status(400).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND")
            });
        }

        // Process each widget permission update
        const updatePromises: any[] = [];
        const widgetOrderUpdates: any[] = [];
        const notFoundWidgets: any[] = [];

        for (const widgetData of widgets) {
            const { widget_id, permission, order } = widgetData;

            if (!widget_id || permission === undefined) {
                continue; // Skip invalid widget data
            }

            // Validate that the widget exists and belongs to the organization
            const widgetExists = await MOWidget.findOne({
                where: {
                    id: widget_id,
                    organization_id: req.user.organization_id
                }
            });

            if (!widgetExists) {
                notFoundWidgets.push(widget_id);
                continue;
            }

            // Check if permission exists
            const existingPermission = await MOPermission.findOne({
                where: {
                    role_id,
                    widget_id,
                    organization_id: req.user.organization_id
                }
            });

            if (existingPermission) {
                // Update existing permission
                updatePromises.push(
                    MOPermission.setHeaders(req).update(
                        {
                            permission: setViewPermission(permission),
                            order: order || 0,
                            updated_by: req.user.id
                        },
                        {
                            where: {
                                id: existingPermission.id,
                                organization_id: req.user.organization_id
                            }
                        }
                    )
                );
            } else {
                // Create new permission if it doesn't exist
                updatePromises.push(
                    MOPermission.setHeaders(req).create({
                        role_id,
                        widget_id,
                        permission: setViewPermission(permission),
                        order: order || 0,
                        organization_id: req.user.organization_id,
                        status: permission_status.ACTIVE,
                        created_by: req.user.id,
                        updated_by: req.user.id
                    } as any)
                );
            }

            // Track order updates for response
            if (order !== undefined) {
                widgetOrderUpdates.push({
                    widget_id,
                    order
                });
            }
        }

        // Execute all permission updates/creates
        await Promise.all(updatePromises);

        return res.status(200).json({
            status: true,
            message: res.__("WIDGET_PERMISSIONS_UPDATED_SUCCESSFULLY"),
            data: {
                role_id,
                updated_widgets: widgets.length,
                updated_orders: widgetOrderUpdates.length,
                not_found_widgets: notFoundWidgets
            }
        });

    } catch (error) {
        console.log('Error in updateWidgetPermission:', error);
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Delete Widget Permission
export const deleteWidgetPermission = async (req: Request, res: Response) => {
    try {
        const { id } = req.params; // role_id

        // Check permission for widget permission deletion
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'permission', // Permission module slug
            ROLE_PERMISSIONS.DELETE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res.status(403).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        // Validate that the role exists and belongs to the organization
        const roleExists = await Role.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id,
                role_status: 'active'
            }
        });

        if (!roleExists) {
            return res.status(400).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND")
            });
        }

        // Delete all widget permissions for the role
        await sequelize.query(
            `DELETE FROM mo_permissions
             WHERE role_id = :role_id
             AND organization_id = :organization_id
             AND widget_id IS NOT NULL`,
            {
                replacements: {
                    role_id: id,
                    organization_id: req.user.organization_id
                },
                type: sequelize.QueryTypes.DELETE
            }
        );

        return res.status(200).json({
            status: true,
            message: res.__("WIDGET_PERMISSIONS_DELETED_SUCCESSFULLY"),
            data: {
                role_id: id
            }
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Copy Widget Permissions
export const copyWidgetPermissions = async (req: Request, res: Response) => {
    try {
        const { from_role, to_role } = req.body;

        // Check permission for widget permission management
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'permission', // Permission module slug
            ROLE_PERMISSIONS.CREATE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res.status(403).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        // Validate input
        if (!from_role || !to_role) {
            return res.status(400).json({
                status: false,
                message: res.__("INVALID_INPUT_DATA")
            });
        }

        // Support copying to multiple roles
        const toRoles = Array.isArray(to_role) ? to_role : [to_role];

        // Validate that all roles exist and belong to the organization
        const allRoleIds = [from_role, ...toRoles];
        const existingRoles = await Role.findAll({
            where: {
                id: { [Op.in]: allRoleIds },
                organization_id: req.user.organization_id,
                role_status: 'active'
            }
        });

        if (existingRoles.length !== allRoleIds.length) {
            return res.status(400).json({
                status: false,
                message: res.__("SOME_ROLES_NOT_FOUND")
            });
        }

        // Get widget permissions from source role using raw query
        const sourcePermissions = await sequelize.query(
            `SELECT * FROM mo_permissions
             WHERE role_id = :from_role
             AND organization_id = :organization_id
             AND widget_id IS NOT NULL
             AND status = 'active'`,
            {
                replacements: {
                    from_role: from_role,
                    organization_id: req.user.organization_id
                },
                type: sequelize.QueryTypes.SELECT
            }
        );

        if (sourcePermissions.length === 0) {
            return res.status(400).json({
                status: false,
                message: res.__("NO_WIDGET_PERMISSIONS_TO_COPY")
            });
        }

        // Copy permissions to target roles
        const copyPromises: any[] = [];

        for (const targetRoleId of toRoles) {
            // Delete existing widget permissions for target role
            await sequelize.query(
                `DELETE FROM mo_permissions
                 WHERE role_id = :role_id
                 AND organization_id = :organization_id
                 AND widget_id IS NOT NULL`,
                {
                    replacements: {
                        role_id: targetRoleId,
                        organization_id: req.user.organization_id
                    },
                    type: sequelize.QueryTypes.DELETE
                }
            );

            // Copy permissions from source role
            for (const sourcePerm of sourcePermissions as any[]) {
                copyPromises.push(
                    MOPermission.setHeaders(req).create({
                        role_id: targetRoleId,
                        widget_id: sourcePerm.widget_id,
                        permission: sourcePerm.permission,
                        order: sourcePerm.order,
                        organization_id: req.user.organization_id,
                        status: permission_status.ACTIVE,
                        created_by: req.user.id,
                        updated_by: req.user.id
                    } as any)
                );
            }
        }

        // Execute all copy operations
        await Promise.all(copyPromises);

        return res.status(200).json({
            status: true,
            message: res.__("WIDGET_PERMISSIONS_COPIED_SUCCESSFULLY"),
            data: {
                from_role,
                to_roles: toRoles,
                copied_permissions: sourcePermissions.length
            }
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Format widget permission response grouped by category
export const formatWidgetPermissions = (permissions: any) => {
    const result: any = {};

    permissions.forEach((perm: any) => {
        // Parse filters if they exist
        let parsedFilters = null;
        if (perm.widget?.filters) {
            try {
                parsedFilters = typeof perm.widget.filters === 'string' ?
                    JSON.parse(perm.widget.filters) : perm.widget.filters;
            } catch (error) {
                parsedFilters = null;
            }
        }

        const permObj = {
            checked: perm.permission > 0,
            permission: perm.permission,
            order: perm.order || 0,
            widget_name: perm?.widget?.name,
            widget_type: perm?.widget?.type,
            widget_subType: perm?.widget?.subType,
            widget_slug: perm?.widget?.slug,
            widget_category: perm?.widget?.widget_category,
            call_api: perm?.widget?.call_api,
            filters: parsedFilters,
            created_by: perm?.widget?.created_by
        };

        const category = perm?.widget?.widget_category || 'self';

        if (!result[category]) {
            result[category] = {};
        }

        result[category][perm.widget.slug] = permObj;
    });

    return result;
};

// Get widget permissions (similar to getPermissions but for widgets)
export const getWidgetPermissions = async (req: Request, res: Response, params: any) => {
    const { id, page, size, search, role_id, platform } = params;

    try {
        const { offset } = getPagination(Number(page), Number(size));

        const whereObj: any = {
            where: {
                status: permission_status.ACTIVE,
                organization_id: role_id == "null" ? { [Op.is]: null } : req.user.organization_id,
                widget_id: { [Op.not]: null } // Only widget permissions
            },
            include: [
                {
                    model: Role,
                    as: 'role',
                    attributes: ['id', 'role_name', 'role_status', 'platform', 'additional_permissions', 'created_by']
                },
                {
                    model: MOWidget,
                    as: 'widget',
                    where: {
                        organization_id: role_id == "null" ? { [Op.is]: null } : req.user.organization_id
                    },
                    attributes: ['id', 'name', 'type', 'subType', 'slug', 'filters', 'widget_category', 'call_api', 'created_by']
                }
            ],
            raw: true,
            nest: true
        };

        if (id) whereObj.where.id = id;
        if (role_id) whereObj.where.role_id = role_id == "null" ? 1 : role_id;
        if (platform) {
            whereObj.include[0].where = { platform: platform };
        }
        if (search) {
            whereObj.include[1].where = {
                ...whereObj.include[1].where,
                [Op.or]: [
                    { name: { [Op.like]: `%${search}%` } },
                    { slug: { [Op.like]: `%${search}%` } },
                    { type: { [Op.like]: `%${search}%` } },
                    { subType: { [Op.like]: `%${search}%` } }
                ]
            };
        }

        const permissions = await MOPermission.findAndCountAll(whereObj);

        // Get unique created_by user IDs from permissions, roles, and widgets
        const permissionCreatedByIds = permissions.rows.map((perm: any) => perm.created_by).filter(Boolean);
        const roleCreatedByIds = permissions.rows.map((perm: any) => perm.role?.created_by).filter(Boolean);
        const widgetCreatedByIds = permissions.rows.map((perm: any) => perm.widget?.created_by).filter(Boolean);

        const allCreatedByIds = [...new Set([...permissionCreatedByIds, ...roleCreatedByIds, ...widgetCreatedByIds])];

        // Fetch user details for all created_by users
        const users = await User.findAll({
            where: { id: { [Op.in]: allCreatedByIds } },
            attributes: [
                "id",
                "user_first_name",
                "user_last_name",
                [
                    sequelize.fn(
                        "concat",
                        sequelize.col("user_first_name"),
                        " ",
                        sequelize.col("user_last_name")
                    ),
                    "user_full_name",
                ],
                [
                    sequelize.literal(
                        `CASE
                        WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
                        WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar)
                        THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
                        ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
                        END`
                    ),
                    "user_avatar",
                ],
            ],
            raw: true
        });

        // Create user map for quick lookup
        const userMap = new Map(users.map(user => [user.id, user]));

        // Add created_by user details to permissions
        const permissionsWithUserDetails = permissions.rows.map((permission: any) => ({
            ...permission,
            created_by: permission.created_by ? userMap.get(permission.created_by) || null : null,
            role: permission.role ? {
                ...permission.role,
                created_by: permission.role.created_by ? userMap.get(permission.role.created_by) || null : null
            } : null,
            widget: permission.widget ? {
                ...permission.widget,
                created_by: permission.widget.created_by ? userMap.get(permission.widget.created_by) || null : null
            } : null
        }));

        // Format the permissions for returning
        const groupedPermission = _.groupBy(permissionsWithUserDetails, 'role_id');

        let formattedPermissions = [];
        for (const roleId in groupedPermission) {
            const permission = groupedPermission[roleId];
            const formattedPermission = formatWidgetPermissions(permission);
            formattedPermissions.push({
                role_id: parseInt(roleId),
                role_name: permission[0]?.role?.role_name,
                permissions: formattedPermission,
                platform: permission[0]?.role?.platform,
                additional_permissions: permission[0]?.role?.additional_permissions ?
                    JSON.parse(permission[0]?.role?.additional_permissions) : {},
                created_by: permission[0]?.role?.created_by || null
            });
        }

        const response = getPaginatedItems(Number(size), Number(page), formattedPermissions.length);

        if (page && size) {
            formattedPermissions = formattedPermissions.slice(offset, page * size);
        }

        return res.status(200).json({
            status: true,
            message: res.__("WIDGET_PERMISSIONS_FETCHED_SUCCESSFULLY"),
            data: formattedPermissions,
            ...response
        });

    } catch (error) {
        console.log(error);
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

export const getPermissions = async (req: Request, res: Response) => {
    const { id } = req.params;
    const { page, size, search = "", role_id = null, platform = null, type }: any = req.query;

    try {
        const { limit, offset } = getPagination(Number(page), Number(size));

        // Check if type is widget to handle widget permissions
        if (type === 'widget') {
            return await getWidgetPermissions(req, res, { id, page, size, search, role_id, platform });
        }

        const moduleWhere: any = {}
        if (type) {
            moduleWhere.type = type;
        }
        if(role_id == "null"){
            moduleWhere.module = { [Op.in]:  ['support_ticket_dashboard', 'support_ticket'] }
        }
        const whereObj: any = {
            where: {
                status: permission_status.ACTIVE,
                organization_id: role_id == "null" ? { [Op.is]: null } : req.user.organization_id
            },
            include: [
                {
                    model: Role,
                    as: 'role',
                    attributes: ['id', 'role_name', 'role_status', 'platform', 'additional_permissions', 'created_by']
                },
                {
                    model: MOModule,
                    as: 'module',
                    where: moduleWhere,
                    attributes: ['id', 'module', 'module_name', 'created_by', 'index']
                }
            ],
            raw: true,
            nest: true
        }


        if (id) whereObj.where.id = id
        if (role_id) whereObj.where.role_id = role_id == "null" ? 1 : role_id;
        if (platform) {
            // Platform filter should be applied to the role, not permission
            whereObj.include[0].where = { platform: platform };
        }
        if (search) {
            whereObj.include[1].where = {
                [Op.or]: [
                    { module: { [Op.like]: `%${search}%` } },
                    { module_name: { [Op.like]: `%${search}%` } }
                ]
            };
        }

        // if (page && size) {
        //     whereObj.limit = limit;
        //     whereObj.offset = offset;
        // }

        const permissions = await MOPermission.findAndCountAll(whereObj);

        // Get unique created_by user IDs from permissions, roles, and modules
        const permissionCreatedByIds = permissions.rows.map((perm: any) => perm.created_by).filter(Boolean);
        const roleCreatedByIds = permissions.rows.map((perm: any) => perm.role?.created_by).filter(Boolean);
        const moduleCreatedByIds = permissions.rows.map((perm: any) => perm.module?.created_by).filter(Boolean);

        const allCreatedByIds = [...new Set([...permissionCreatedByIds, ...roleCreatedByIds, ...moduleCreatedByIds])];

        // Fetch user details for all created_by users
        const users = await User.findAll({
            where: { id: { [Op.in]: allCreatedByIds } },
            attributes: [
                "id",
                "user_first_name",
                "user_last_name",
                [
                    sequelize.fn(
                        "concat",
                        sequelize.col("user_first_name"),
                        " ",
                        sequelize.col("user_last_name")
                    ),
                    "user_full_name",
                ],
                [
                    sequelize.literal(
                        `CASE
                        WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
                        WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar)
                        THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
                        ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
                        END`
                    ),
                    "user_avatar",
                ],
            ],
            raw: true
        });

        // Create user map for quick lookup
        const userMap = new Map(users.map(user => [user.id, user]));

        // Add created_by user details to permissions
        const permissionsWithUserDetails = permissions.rows.map((permission: any) => ({
            ...permission,
            created_by: permission.created_by ? userMap.get(permission.created_by) || null : null,
            role: permission.role ? {
                ...permission.role,
                created_by: permission.role.created_by ? userMap.get(permission.role.created_by) || null : null
            } : null,
            module: permission.module ? {
                ...permission.module,
                created_by: permission.module.created_by ? userMap.get(permission.module.created_by) || null : null
            } : null
        }));

        // Format the permissions for returning
        const groupedPermission = _.groupBy(permissionsWithUserDetails, 'role_id')

        let formattedPermissions = []
        for (const roleId in groupedPermission) {
            const permission = groupedPermission[roleId];
            const formattedPermission = formatPermissions(permission)
            formattedPermissions.push({
                role_id: parseInt(roleId),
                role_name: permission[0]?.role?.role_name,
                permissions: formattedPermission,
                platform: permission[0]?.role?.platform,
                additional_permissions: permission[0]?.role?.additional_permissions ? JSON.parse(permission[0]?.role?.additional_permissions) : {},
                created_by: permission[0]?.role?.created_by || null
            })
        }
        const response = getPaginatedItems(Number(size), Number(page), formattedPermissions.length);

        if (page && size) {
            formattedPermissions = formattedPermissions.slice(offset, page * size);
        }

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_FETCHED_SUCCESSFULLY"),
            data: formattedPermissions,
            ...response
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


export const deletePermission = async (req: Request, res: Response) => {
    const { id } = req.params; // id is role_id

    try {
        // Try new MORole-based permission system first, then fallback to old system
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'permission', // Role module slug
            ROLE_PERMISSIONS.DELETE,
            req?.headers?.['platform-type'] as string
        );

        if (!checkModulePermission) {
            return res.status(403).json({
                status: false,
                message: res.__("PERMISSION_DENIED")
            });
        }

        // Verify role exists and belongs to organization
        const roleRecord = await Role.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        if (!roleRecord) {
            return res.status(404).json({
                status: false,
                message: res.__("ROLE_NOT_FOUND")
            });
        }

        // Get all permissions for this role to check if any exist
        const existingPermissions = await MOPermission.findAll({
            where: {
                role_id: id,
                organization_id: req.user.organization_id,
                status: permission_status.ACTIVE
            }
        });

        // Delete all module permissions for this role (set status to INACTIVE)
        await MOPermission.setHeaders(req).update(
            {
                status: permission_status.INACTIVE,
                updated_by: req.user.id
            } as any,
            {
                where: {
                    role_id: id,
                    organization_id: req.user.organization_id
                }
            }
        );

        // Clear additional_permissions for this role
        await Role.setHeaders(req).update(
            {
                additional_permissions: null,
                updated_by: req.user.id
            } as any,
            {
                where: {
                    id: id,
                    organization_id: req.user.organization_id
                }
            }
        );

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_DELETED_SUCCESSFULLY"),
            data: {
                role_id: id,
                permissions_deleted: existingPermissions.length,
                additional_permissions_cleared: true
            }
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


export const copyPermissions = async (req: Request, res: Response) => {
    const { from_role, to_role } = req.body; // to_role is now an array

    try {
        // Validate input
        if (!Array.isArray(to_role) || to_role.length === 0) {
            return res.status(400).json({
                status: false,
                message: res.__("TO_ROLE_MUST_BE_NON_EMPTY_ARRAY")
            });
        }

        // Step 1: Fetch source role with platform and additional_permissions
        const fromRole = await Role.findOne({
            where: {
                id: from_role,
                organization_id: req.user.organization_id,
                role_status: 'active'
            },
            attributes: ['id', 'role_name', 'platform', 'additional_permissions']
        });

        if (!fromRole) {
            return res.status(404).json({
                status: false,
                message: res.__("FROM_ROLE_NOT_FOUND")
            });
        }

        // Step 2: Verify all destination roles exist
        const toRoles = await Role.findAll({
            where: {
                id: { [Op.in]: to_role },
                organization_id: req.user.organization_id,
                role_status: 'active'
            },
            attributes: ['id', 'role_name']
        });

        if (toRoles.length !== to_role.length) {
            const foundRoleIds = toRoles.map(role => role.id);
            const missingRoleIds = to_role.filter(id => !foundRoleIds.includes(id));
            return res.status(404).json({
                status: false,
                message: res.__("SOME_TO_ROLES_NOT_FOUND"),
                missing_role_ids: missingRoleIds
            });
        }

        // Step 3: Fetch all permissions for the from_role
        const fromPermissions = await MOPermission.findAll({
            where: {
                role_id: from_role,
                organization_id: req.user.organization_id
            }
        });

        if (!fromPermissions.length) {
            return res.status(404).json({
                status: false,
                message: res.__("NO_PERMISSIONS_FOUND_FOR_FROM_ROLE")
            });
        }

        // Step 4: Delete existing module permissions for destination roles to allow replacement
        for (const roleId of to_role) {
            await sequelize.query(
                `DELETE FROM mo_permissions
                 WHERE role_id = :role_id
                 AND organization_id = :organization_id
                 AND module_id IS NOT NULL`,
                {
                    replacements: {
                        role_id: roleId,
                        organization_id: req.user.organization_id
                    },
                    type: sequelize.QueryTypes.DELETE
                }
            );
        }

        await sequelize.transaction(async (t: Transaction) => {
            // Step 5: Copy role-level platform and additional_permissions to all destination roles
            for (const roleId of to_role) {
                await Role.setHeaders(req).update(
                    {
                        platform: fromRole.platform,
                        additional_permissions: fromRole.additional_permissions,
                        updated_by: req.user.id
                    },
                    {
                        where: {
                            id: roleId,
                            organization_id: req.user.organization_id
                        },
                        transaction: t
                    }
                );

                // Step 6: Copy all permissions to this role
                for (const permission of fromPermissions) {
                    await MOPermission.create(
                        {
                            role_id: roleId,
                            module_id: permission.module_id,
                            permission: permission.permission,
                            partial: permission.partial,
                            created_by: req.user.id,
                            updated_by: req.user.id,
                            organization_id: req.user.organization_id
                        } as any,
                        { transaction: t }
                    );
                }
            }
        });

        return res.status(200).json({
            status: true,
            message: res.__("PERMISSIONS_AND_ROLE_SETTINGS_COPIED_SUCCESSFULLY"),
            data: {
                from_role,
                to_roles: to_role,
                copied_permissions: fromPermissions.length,
                replaced_existing: true
            }
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// ==================== MODULE CRUD APIs ====================

// Create Module
export const createModule = async (req: Request, res: Response) => {
    try {
        const { module, module_name } = req.body;

        // Duplicate validation
        const existingModule = await MOModule.findOne({
            where: { module, organization_id: req.user.organization_id }
        });

        if (existingModule) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS"),
            });
        }

        const newModule = await MOModule.setHeaders(req).create({
            module,
            module_name,
            organization_id: req.user.organization_id,
            created_by: req.user.id,
            updated_by: req.user.id
        } as any);

        return res.status(201).json({
            status: true,
            message: res.__("MODULE_CREATED_SUCCESSFULLY"),
            data: newModule
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Modules
export const getModules = async (req: Request, res: Response) => {
    try {
        const { search, page, size, id, type, role_id } = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));

        // Check if type is widget, then return widgets instead of modules
        if (type === 'widget') {
            const whereClause: any = {
                where: {
                    organization_id: req.user.organization_id
                },
                order: [['index', 'ASC']], // Order by index for organization-scoped ordering
                raw: false
            };

            if (page && size) {
                whereClause.limit = limit;
                whereClause.offset = offset;
            }

            if (id) {
                whereClause.where.id = id;
            }

            if (search) {
                whereClause.where[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { slug: { [Op.like]: `%${search}%` } },
                    { type: { [Op.like]: `%${search}%` } },
                    { subType: { [Op.like]: `%${search}%` } }
                ];
            }

            const widgets = await MOWidget.findAndCountAll(whereClause);

            // If role_id is provided, filter widgets based on permissions
            let filteredWidgets = widgets.rows;
            if (role_id) {
                // Get widget permissions for the role using raw query
                const widgetPermissions = await sequelize.query(
                    `SELECT p.*, w.* FROM mo_permissions p
                     INNER JOIN mo_widgets w ON p.widget_id = w.id
                     WHERE p.role_id = :role_id
                     AND p.organization_id = :organization_id
                     AND p.status = 'active'
                     AND p.widget_id IS NOT NULL
                     AND w.organization_id = :organization_id
                     ORDER BY p.order ASC`,
                    {
                        replacements: {
                            role_id: Number(role_id),
                            organization_id: req.user.organization_id
                        },
                        type: sequelize.QueryTypes.SELECT
                    }
                );

                // Create a map of widget permissions
                const permissionMap = new Map();
                (widgetPermissions as any[]).forEach((perm: any) => {
                    permissionMap.set(perm.widget_id, {
                        permission: perm.permission || 1, // Default to view permission
                        order: perm.order || 0
                    });
                });

                // Filter and sort widgets based on permissions
                filteredWidgets = widgets.rows
                    .filter((widget: any) => permissionMap.has(widget.id))
                    .map((widget: any) => ({
                        ...widget.toJSON(),
                        checked: permissionMap.get(widget.id).permission > 0,
                        order: permissionMap.get(widget.id).order
                    }))
                    .sort((a: any, b: any) => a.order - b.order);
            } else {
                // Default permission to 1 (view) when no role_id is provided
                filteredWidgets = widgets.rows.map((widget: any) => ({
                    ...widget.toJSON()
                }));
            }

            // Get unique created_by user IDs
            const createdByUserIds = [...new Set(filteredWidgets.map((widget: any) => widget.created_by).filter(Boolean))];

            // Fetch user details for created_by users
            const users = await User.findAll({
                where: { id: { [Op.in]: createdByUserIds } },
                attributes: [
                    "id",
                    "user_first_name",
                    "user_last_name",
                    [
                        sequelize.fn(
                            "concat",
                            sequelize.col("user_first_name"),
                            " ",
                            sequelize.col("user_last_name")
                        ),
                        "user_full_name",
                    ],
                    [
                        sequelize.literal(
                            `CASE
                            WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
                            WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar)
                            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
                            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
                            END`
                        ),
                        "user_avatar",
                    ],
                ],
                raw: true
            });

            // Create user map for quick lookup
            const userMap = new Map(users.map(user => [user.id, user]));

            // Add created_by user details to widgets and parse filters
            const widgetsWithUserDetails = filteredWidgets.map((widget: any) => {
                let parsedFilters = null;
                if (widget.filters) {
                    try {
                        parsedFilters = typeof widget.filters === 'string' ? JSON.parse(widget.filters) : widget.filters;
                    } catch (error) {
                        parsedFilters = null; // If parsing fails, set to null
                    }
                }

                return {
                    ...widget,
                    filters: parsedFilters,
                    created_by: widget.created_by ? userMap.get(widget.created_by) || null : null
                };
            });

            const response = getPaginatedItems(Number(size), Number(page), filteredWidgets.length);

            const data = {
                data: widgetsWithUserDetails,
                ...response
            };

            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("SUCCESS_FETCHED"),
                ...data,
            });
        }

        // Original module logic
        const whereClause: any = {
            where: {
                organization_id: req.user.organization_id
            },
            order: [['index', 'ASC']], // Order by index for organization-scoped ordering
            raw: true
        };

        if (page && size) {
            whereClause.limit = limit;
            whereClause.offset = offset;
        }

        if (id) {
            whereClause.where.id = id;
        }

        if (type) {
            whereClause.where.type = type;
        }

        if (search) {
            whereClause.where[Op.or] = [
                { module: { [Op.like]: `%${search}%` } },
                { module_name: { [Op.like]: `%${search}%` } }
            ];
        }

        const modules = await MOModule.findAndCountAll(whereClause);

        // Get unique created_by user IDs
        const createdByUserIds = [...new Set(modules.rows.map((module: any) => module.created_by).filter(Boolean))];

        // Fetch user details for created_by users
        const users = await User.findAll({
            where: { id: { [Op.in]: createdByUserIds } },
            attributes: [
                "id",
                "user_first_name",
                "user_last_name",
                [
                    sequelize.fn(
                        "concat",
                        sequelize.col("user_first_name"),
                        " ",
                        sequelize.col("user_last_name")
                    ),
                    "user_full_name",
                ],
                [
                    sequelize.literal(
                        `CASE
                        WHEN user_avatar IS NULL OR user_avatar = '' THEN ''
                        WHEN NOT user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = user_avatar)
                        THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', user_avatar)
                        ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar))
                        END`
                    ),
                    "user_avatar",
                ],
            ],
            raw: true
        });

        // Create user map for quick lookup
        const userMap = new Map(users.map(user => [user.id, user]));

        // Add created_by user details to modules
        const modulesWithUserDetails = modules.rows.map((module: any) => ({
            ...module,
            created_by: module.created_by ? userMap.get(module.created_by) || null : null
        }));

        const response = getPaginatedItems(Number(size), Number(page), modules?.count);

        const data = {
            data: modulesWithUserDetails,
            ...response
        }

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Update Module
export const updateModule = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { module, module_name } = req.body;

        // Check if module exists
        const existingModule = await MOModule.findByPk(id);

        if (!existingModule) {
            return res.status(404).json({
                status: false,
                message: res.__("MODULE_NOT_FOUND"),
            });
        }

        // Duplicate validation
        const duplicateModule = await MOModule.findOne({
            where: {
                module,
                organization_id: req.user.organization_id,
                id: { [Op.ne]: id }
            }
        });

        if (duplicateModule) {
            return res.status(400).json({
                status: false,
                message: res.__("MODULE_ALREADY_EXISTS"),
            });
        }

        const updateObj = {
            module: module ? module : existingModule.module,
            module_name: module_name ? module_name : existingModule.module_name,
            updated_by: req.user.id
        }

        await MOModule.setHeaders(req).update(updateObj, {
            where: { id: id }
        });

        return res.status(200).json({
            status: true,
            message: res.__("MODULE_UPDATED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Delete Module
export const deleteModule = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        const module = await MOModule.findByPk(id);

        if (!module) {
            return res.status(404).json({
                status: false,
                message: res.__("MODULE_NOT_FOUND"),
            });
        }

        await MOModule.setHeaders(req).destroy({
            where: { id: id }
        });

        return res.status(200).json({
            status: true,
            message: res.__("MODULE_DELETED_SUCCESSFULLY")
        });
    } catch (error) {
        console.log(error)
        return res.status(500).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// ==================== WIDGET MANAGEMENT FUNCTIONS ====================

// Create Widget
export const createWidget = async (req: Request, res: Response) => {
    try {
        const { name, widget, type, subType, filters, slug, call_api, widget_category } = req.body;

        // Check permission for widget creation
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'widget', // Widget module slug
            ROLE_PERMISSIONS.CREATE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        // Validate required fields
        if (!name || !widget || !type || !subType || !slug) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("REQUIRED_FIELDS_MISSING"),
                data: { required: ['name', 'widget', 'type', 'subType', 'slug'] }
            });
        }

        // Validate type and subType
        if (!Object.values(widget_type).includes(type)) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_WIDGET_TYPE"),
                data: { validTypes: Object.values(widget_type) }
            });
        }

        if (!Object.values(widget_sub_type).includes(subType)) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_WIDGET_SUB_TYPE"),
                data: { validSubTypes: Object.values(widget_sub_type) }
            });
        }

        // Validate widget_category if provided
        if (widget_category && !Object.values(widget_category).includes(widget_category)) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_WIDGET_CATEGORY"),
                data: { validCategories: Object.values(widget_category) }
            });
        }

        // Check for duplicate slug within organization
        const existingWidget = await MOWidget.findOne({
            where: {
                slug,
                organization_id: req.user.organization_id
            }
        });

        if (existingWidget) {
            return res.status(StatusCodes.CONFLICT).json({
                status: false,
                message: res.__("WIDGET_SLUG_ALREADY_EXISTS")
            });
        }

        // Validate and stringify filters if provided
        let stringifiedFilters = null;
        if (filters) {
            try {
                // If filters is already a string, validate it's valid JSON
                if (typeof filters === 'string') {
                    JSON.parse(filters); // Validate JSON
                    stringifiedFilters = filters;
                } else {
                    // If filters is an object/array, stringify it
                    stringifiedFilters = JSON.stringify(filters);
                }
            } catch (error) {
                return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("INVALID_FILTERS_FORMAT")
                });
            }
        }

        // Create widget
        const newWidget = await MOWidget.setHeaders(req).create({
            name,
            widget,
            type,
            subType,
            filters: stringifiedFilters,
            slug,
            call_api: call_api || false,
            widget_category: widget_category || widget_category.SELF,
            organization_id: req.user.organization_id,
            created_by: req.user.id,
            updated_by: req.user.id
        } as any);

        return res.status(StatusCodes.CREATED).json({
            status: true,
            message: res.__("WIDGET_CREATED_SUCCESSFULLY"),
            data: newWidget
        });

    } catch (error: any) {
        console.error("Error in createWidget:", error);

        // Handle validation errors
        if (error.name === 'SequelizeValidationError') {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("VALIDATION_ERROR"),
                data: error.errors.map((err: any) => ({
                    field: err.path,
                    message: err.message
                }))
            });
        }

        // Handle unique constraint errors
        if (error.name === 'SequelizeUniqueConstraintError') {
            return res.status(StatusCodes.CONFLICT).json({
                status: false,
                message: res.__("WIDGET_SLUG_ALREADY_EXISTS")
            });
        }

        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Widgets with role-based permission filtering
export const getWidgets = async (req: Request, res: Response) => {
    try {
        const { search, page, size, id, type, role_id } = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));

        // Check permission for widget viewing
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'widget', // Widget module slug
            ROLE_PERMISSIONS.VIEW,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const whereClause: any = {
            where: {
                organization_id: req.user.organization_id
            },
            order: [['index', 'ASC']], // Order by index for organization-scoped ordering
            raw: false
        };

        if (page && size) {
            whereClause.limit = limit;
            whereClause.offset = offset;
        }

        if (id) {
            whereClause.where.id = id;
        }

        if (type) {
            whereClause.where.type = type;
        }

        if (search) {
            whereClause.where[Op.or] = [
                { name: { [Op.like]: `%${search}%` } },
                { slug: { [Op.like]: `%${search}%` } },
                { type: { [Op.like]: `%${search}%` } },
                { subType: { [Op.like]: `%${search}%` } }
            ];
        }

        // Get widgets
        const widgets = await MOWidget.findAndCountAll(whereClause);

        // If role_id is provided, filter widgets based on permissions
        let filteredWidgets = widgets.rows;
        if (role_id) {
            // Get widget permissions for the role using raw query
            const widgetPermissions = await sequelize.query(
                `SELECT p.*, w.* FROM mo_permissions p
                 INNER JOIN mo_widgets w ON p.widget_id = w.id
                 WHERE p.role_id = :role_id
                 AND p.organization_id = :organization_id
                 AND p.status = 'active'
                 AND p.widget_id IS NOT NULL
                 AND w.organization_id = :organization_id
                 ORDER BY p.order ASC`,
                {
                    replacements: {
                        role_id: Number(role_id),
                        organization_id: req.user.organization_id
                    },
                    type: sequelize.QueryTypes.SELECT
                }
            );

            // Create a map of widget permissions
            const permissionMap = new Map();
            (widgetPermissions as any[]).forEach((perm: any) => {
                permissionMap.set(perm.widget_id, {
                    permission: perm.permission || 1, // Default to view permission
                    order: perm.order || 0
                });
            });

            // Filter and sort widgets based on permissions
            filteredWidgets = widgets.rows
                .filter((widget: any) => permissionMap.has(widget.id))
                .map((widget: any) => ({
                    ...widget.toJSON(),
                    checked: permissionMap.get(widget.id).permission,
                    order: permissionMap.get(widget.id).order
                }))
                .sort((a: any, b: any) => a.order - b.order);
        } else {
            // Default permission to 1 (view) when no role_id is provided
            filteredWidgets = widgets.rows.map((widget: any) => ({
                ...widget.toJSON(),
                checked: 1,
                order: 0
            }));
        }

        // Get created_by user details
        const createdByIds = [...new Set(filteredWidgets.map((widget: any) => widget.created_by).filter(Boolean))];
        const users = await User.findAll({
            where: { id: { [Op.in]: createdByIds } },
            attributes: ['id', 'user_first_name', 'user_last_name', 'user_full_name', 'user_avatar'],
            raw: true
        });

        // Create user map for quick lookup
        const userMap = new Map(users.map(user => [user.id, user]));

        // Add created_by user details to widgets and parse filters
        const widgetsWithUserDetails = filteredWidgets.map((widget: any) => {
            let parsedFilters = null;
            if (widget.filters) {
                try {
                    parsedFilters = typeof widget.filters === 'string' ? JSON.parse(widget.filters) : widget.filters;
                } catch (error) {
                    parsedFilters = null; // If parsing fails, set to null
                }
            }

            return {
                ...widget,
                filters: parsedFilters,
                created_by: widget.created_by ? userMap.get(widget.created_by) || null : null
            };
        });

        const response = getPaginatedItems(Number(size), Number(page), filteredWidgets.length);

        const data = {
            data: widgetsWithUserDetails,
            ...response
        };

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            ...data,
        });

    } catch (error) {
        console.error("Error in getWidgets:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Update Widget
export const updateWidget = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const { name, widget, type, subType, filters, slug, call_api, widget_category } = req.body;

        // Check permission for widget editing
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'widget', // Widget module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        // Find existing widget
        const existingWidget = await MOWidget.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        if (!existingWidget) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("WIDGET_NOT_FOUND")
            });
        }

        // Validate type and subType if provided
        if (type && !Object.values(widget_type).includes(type)) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_WIDGET_TYPE"),
                data: { validTypes: Object.values(widget_type) }
            });
        }

        if (subType && !Object.values(widget_sub_type).includes(subType)) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_WIDGET_SUB_TYPE"),
                data: { validSubTypes: Object.values(widget_sub_type) }
            });
        }

        // Validate widget_category if provided
        if (widget_category && !Object.values(widget_category).includes(widget_category)) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_WIDGET_CATEGORY"),
                data: { validCategories: Object.values(widget_category) }
            });
        }

        // Check for duplicate slug if slug is being updated
        if (slug && slug !== existingWidget.slug) {
            const duplicateWidget = await MOWidget.findOne({
                where: {
                    slug,
                    organization_id: req.user.organization_id,
                    id: { [Op.ne]: id }
                }
            });

            if (duplicateWidget) {
                return res.status(StatusCodes.CONFLICT).json({
                    status: false,
                    message: res.__("WIDGET_SLUG_ALREADY_EXISTS")
                });
            }
        }

        // Validate and stringify filters if provided
        let stringifiedFilters = existingWidget.filters;
        if (filters !== undefined) {
            if (filters === null) {
                stringifiedFilters = null;
            } else {
                try {
                    // If filters is already a string, validate it's valid JSON
                    if (typeof filters === 'string') {
                        JSON.parse(filters); // Validate JSON
                        stringifiedFilters = filters;
                    } else {
                        // If filters is an object/array, stringify it
                        stringifiedFilters = JSON.stringify(filters);
                    }
                } catch (error) {
                    return res.status(StatusCodes.BAD_REQUEST).json({
                        status: false,
                        message: res.__("INVALID_FILTERS_FORMAT")
                    });
                }
            }
        }

        // Update widget
        const updateData: any = {
            updated_by: req.user.id
        };

        if (name !== undefined) updateData.name = name;
        if (widget !== undefined) updateData.widget = widget;
        if (type !== undefined) updateData.type = type;
        if (subType !== undefined) updateData.subType = subType;
        if (filters !== undefined) updateData.filters = stringifiedFilters;
        if (slug !== undefined) updateData.slug = slug;
        if (call_api !== undefined) updateData.call_api = call_api;
        if (widget_category !== undefined) updateData.widget_category = widget_category;

        await MOWidget.setHeaders(req).update(
            updateData,
            {
                where: {
                    id: id,
                    organization_id: req.user.organization_id
                }
            }
        );

        // Fetch updated widget
        const updatedWidget = await MOWidget.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("WIDGET_UPDATED_SUCCESSFULLY"),
            data: updatedWidget
        });

    } catch (error: any) {
        console.error("Error in updateWidget:", error);

        // Handle validation errors
        if (error.name === 'SequelizeValidationError') {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("VALIDATION_ERROR"),
                data: error.errors.map((err: any) => ({
                    field: err.path,
                    message: err.message
                }))
            });
        }

        // Handle unique constraint errors
        if (error.name === 'SequelizeUniqueConstraintError') {
            return res.status(StatusCodes.CONFLICT).json({
                status: false,
                message: res.__("WIDGET_SLUG_ALREADY_EXISTS")
            });
        }

        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Delete Widget
export const deleteWidget = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        // Check permission for widget deletion
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'widget', // Widget module slug
            ROLE_PERMISSIONS.DELETE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        // Find existing widget
        const existingWidget = await MOWidget.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        if (!existingWidget) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("WIDGET_NOT_FOUND")
            });
        }

        // Check if widget is being used in permissions
        const widgetPermissions = await sequelize.query(
            `SELECT COUNT(*) as count FROM mo_permissions
             WHERE widget_id = :widget_id
             AND organization_id = :organization_id
             AND status = 'active'`,
            {
                replacements: {
                    widget_id: id,
                    organization_id: req.user.organization_id
                },
                type: sequelize.QueryTypes.SELECT
            }
        );

        const permissionCount = (widgetPermissions as any[])[0]?.count || 0;
        if (permissionCount > 0) {
            return res.status(StatusCodes.CONFLICT).json({
                status: false,
                message: res.__("WIDGET_IN_USE_CANNOT_DELETE"),
                data: {
                    permissionsCount: permissionCount,
                    message: "Widget is assigned to roles and cannot be deleted. Remove widget permissions first."
                }
            });
        }

        // Delete widget
        await MOWidget.destroy({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("WIDGET_DELETED_SUCCESSFULLY")
        });

    } catch (error) {
        console.error("Error in deleteWidget:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Widget by ID
export const getWidgetById = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;

        // Check permission for widget viewing
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'widget', // Widget module slug
            ROLE_PERMISSIONS.VIEW,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        // Find widget
        const widget = await MOWidget.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        if (!widget) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("WIDGET_NOT_FOUND")
            });
        }

        // Get created_by user details
        let createdByUser = null;
        if (widget.created_by) {
            createdByUser = await User.findOne({
                where: { id: widget.created_by },
                attributes: ['id', 'user_first_name', 'user_last_name', 'user_full_name', 'user_avatar'],
                raw: true
            });
        }

        // Parse filters if they exist
        let parsedFilters = null;
        if (widget.filters) {
            try {
                parsedFilters = typeof widget.filters === 'string' ? JSON.parse(widget.filters) : widget.filters;
            } catch (error) {
                parsedFilters = null; // If parsing fails, set to null
            }
        }

        const widgetData = {
            ...widget.toJSON(),
            filters: parsedFilters,
            created_by: createdByUser
        };

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: widgetData
        });

    } catch (error) {
        console.error("Error in getWidgetById:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// ==================== UTILITY FUNCTIONS ====================

// Update Indexes API
export const updateIndexes = async (req: Request, res: Response) => {
    try {
        // Check permission for index update (super admin only)
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        // Call the updateIndexesAPI function
        await updateIndexesAPI(req, res);

    } catch (error) {
        console.error("Error in updateIndexes API:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Migrate Widgets for Organization
export const migrateWidgets = async (req: Request, res: Response) => {
    try {
        // Check permission for widget migration (super admin only)
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'role', // Role module slug
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { organization_id } = req.query;
        const targetOrgId = organization_id as string || req.user.organization_id;

        console.log(`🎨 Starting widget migration for organization: ${targetOrgId}`);

        const result = await createDefaultWidgetsAndPermissions(targetOrgId, req.user.id);

        return res.status(StatusCodes.OK).json({
            status: true,
            message: "Widget migration completed successfully",
            data: {
                organization_id: targetOrgId,
                widgetsCreated: result.widgetsCreated,
                widgetsSkipped: result.widgetsSkipped,
                permissionsCreated: result.permissionsCreated,
                permissionsSkipped: result.permissionsSkipped
            }
        });

    } catch (error) {
        console.error("Error in migrateWidgets API:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};
