"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface moduleAttributes {
  id: number;
  index: number;
  module: string;
  module_name: string;
  organization_id: string;
  created_by: number;
  updated_by: number;
}

export enum module_status {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export class MOModule
  extends Model<moduleAttributes, never>
  implements moduleAttributes
{
  id!: number;
  index!: number;
  module!: string;
  module_name!: string;
  organization_id!: string;
  created_by!: number;
  updated_by!: number;

  public static setHeaders(headers: any) {
    this.beforeCreate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    this.beforeUpdate(function (data: any) {
      (data as any).headers = headers;
      return data;
    });

    return this;
  }

  // Auto-generate index based on organization_id
  public static async generateIndex(organization_id: string): Promise<number> {
    const lastModule = await MOModule.findOne({
      where: { organization_id },
      order: [['index', 'DESC']],
      attributes: ['index']
    });

    return lastModule ? lastModule.index + 1 : 1;
  }
}

MOModule.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    index: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
    },
    module: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    module_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    organization_id: {
      type: DataTypes.STRING,
    },
    created_by: {
      type: DataTypes.INTEGER,
    },
    updated_by: {
      type: DataTypes.INTEGER,
    },
  },
  {
    sequelize: sequelize,
    tableName: "mo_modules",
    modelName: "MOModule",
    timestamps: true,
  }
);

// Auto-generate index before creating a module
MOModule.addHook("beforeCreate", async (module: any) => {
  if (!module.index && module.organization_id) {
    module.index = await MOModule.generateIndex(module.organization_id);
  }
});

MOModule.addHook("afterCreate", async (module: MOModule) => {
  await addActivity("MOModule", "created", module);
});

MOModule.addHook("afterUpdate", async (module: any) => {
  await addActivity("MOModule", "updated", module);
});


