import { Request, Response } from "express";
import { BannerConfig } from "../models/BannerConfig";
import { StatusCodes } from "http-status-codes";
import { BannerNotification } from "../models/BannerNotification";
import { validateModulePermission } from "../helper/common";
import { ROLE_PERMISSIONS } from "../helper/constant";

// Create BannerConfig
const createBannerConfig = async (req: Request, res: Response) => {
  try {
    // Check module permission for staff notification management
    const checkModulePermission = await validateModulePermission(
      req.user,
      req.user.organization_id,
      'staff_notification',
      ROLE_PERMISSIONS.CREATE
    );

    if (!checkModulePermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: res.__("PERMISSION_DENIED")
      });
    }

    const { key, value } = req.body;
    const userId = req.user?.id;

    const bannerConfig = await BannerConfig.create({
      key,
      value,
      created_by: userId,
    } as any);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_CONFIG_CREATION"),
      data: bannerConfig,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Get All BannerConfigs
const getAllBannerConfigs = async (req: Request, res: Response) => {
  try {
    const bannerConfigs = await BannerConfig.findAll({
      order: [["createdAt", "DESC"]],
    });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_CONFIG_FETCHED"),
      data: bannerConfigs,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Get Single BannerConfig
const getBannerConfigById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const bannerConfig = await BannerConfig.findByPk(id);

    if (!bannerConfig) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ERROR_BANNER_CONFIG_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_CONFIG_FETCHED"),
      data: bannerConfig,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Update BannerConfig
const updateBannerConfig = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { key, value } = req.body;
    const userId = req.user?.id;

    const bannerConfig = await BannerConfig.findByPk(id);

    if (!bannerConfig) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ERROR_BANNER_CONFIG_NOT_FOUND"),
      });
    }

    await bannerConfig.update({
      key,
      value,
      updated_by: userId,
    });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_CONFIG_UPDATION"),
      data: bannerConfig,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Delete BannerConfig
const deleteBannerConfig = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const bannerConfig = await BannerConfig.findByPk(id);

    if (!bannerConfig) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ERROR_BANNER_CONFIG_NOT_FOUND"),
      });
    }

    await bannerConfig.destroy();

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_CONFIG_DELETION"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Get BannerConfig by Key
const getBannerConfigByKey = async (req: Request, res: Response) => {
  try {
    const { key } = req.params;

    const bannerConfig = await BannerConfig.findOne({
      where: { key },
    });

    if (!bannerConfig) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ERROR_BANNER_CONFIG_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_CONFIG_FETCHED"),
      data: bannerConfig,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/** Get BannerNotifications By userId */
const getBannerNotifications = async (req: Request, res: Response) => {
  try {
    const { user_id } = req.params;
    const { read_notification } = req.query;

    const whereObj: any = { created_by: user_id };

    // Handle read_notification filter
    if (read_notification !== undefined) {
      // Convert string 'true'/'false' to boolean and then to number (0/1)
      const isRead =
        typeof read_notification === "string"
          ? read_notification.toLowerCase() === "true"
          : Boolean(read_notification);

      whereObj.read_notification = isRead ? 1 : 0;
    }

    // Step 1: Fetch all user-specific banner notifications
    const bannerNotifications = await BannerNotification.findAll({
      where: whereObj,
      raw: true,
    });

    // Step 2: Prepare array to hold final response
    const responseData = [];

    for (const notification of bannerNotifications) {
      // Clone notification object so we can add banner_config
      const notificationWithConfig: any = { ...notification };

      // Get banner config using banner_config_id
      if (notification.banner_config_id) {
        const bannerConfig = await BannerConfig.findOne({
          attributes: ["id", "key", "value"],
          where: { id: notification.banner_config_id },
          raw: true,
        });

        if (bannerConfig) {
          notificationWithConfig.banner_config = bannerConfig;
        }
      }

      // Push modified object to response
      responseData.push(notificationWithConfig);
    }

    // Step 3: Send response
    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: responseData,
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Create BannerNotification
const createBannerNotification = async (req: Request, res: Response) => {
  try {
    const {
      title,
      description,
      extra_information,
      banner_config_id,
      type,
      start_date,
      end_date,
      is_active,
    } = req.body;
    const userId = req.user?.id;

    // Create dynamic object for banner notification
    const bannerNotificationData: any = {
      title,
      description,
      extra_information,
      type,
      start_date,
      end_date,
      is_active,
      created_by: userId,
      updated_by: userId,
    };

    // Check if banner config exists based on extra_information
    if (
      extra_information === "trial_plan" ||
      extra_information === "cancel_plan"
    ) {
      const whereObj: any = {};
      if (extra_information === "trial_plan") {
        whereObj.key = "subscription";
      } else if (extra_information === "cancel_plan") {
        whereObj.key = "subscription_cancel";
      }

      const bannerConfig = await BannerConfig.findOne({
        where: whereObj,
      });

      if (bannerConfig) {
        bannerNotificationData.banner_config_id = bannerConfig.id;
      }
    } else if (banner_config_id) {
      // If banner_config_id is provided directly, use it
      bannerNotificationData.banner_config_id = banner_config_id;
    }

    const bannerNotification = await BannerNotification.create(
      bannerNotificationData,
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_CREATION"),
      data: bannerNotification,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Get Single BannerNotification
const getBannerNotificationById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const bannerNotification = await BannerNotification.findOne({
      where: { id: id },
      raw: true,
    });

    if (!bannerNotification) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ERROR_BANNER_NOT_FOUND"),
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: bannerNotification,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Update BannerNotification
const updateBannerNotification = async (req: Request, res: Response) => {
  try {
    //extra_information is stringify by FE team
    const { id } = req.params;
    const { title, description, extra_information, banner_config_id } =
      req.body;
    const userId = req.user?.id;

    const bannerNotification = await BannerNotification.findByPk(id);

    if (!bannerNotification) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ERROR_BANNER_NOT_FOUND"),
      });
    }

    await bannerNotification.update({
      title,
      description,
      extra_information,
      banner_config_id,
      updated_by: userId,
    });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_UPDATION"),
      data: bannerNotification,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

// Delete BannerNotification
const deleteBannerNotification = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const bannerNotification = await BannerNotification.findByPk(id);

    if (!bannerNotification) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ERROR_BANNER_NOT_FOUND"),
      });
    }

    await bannerNotification.destroy();

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_DELETION"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/** Mark as Read */
const markAsReadNotification = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    const bannerNotification = await BannerNotification.findOne({
      where: { id: id, created_by: userId },
      raw: true,
    });

    if (!bannerNotification) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: res.__("ERROR_BANNER_NOT_FOUND"),
      });
    }

    await BannerNotification.update(
      {
        read_notification: true,
        updated_by: userId,
      },
      { where: { id: id } },
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: res.__("SUCCESS_BANNER_READ"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  createBannerConfig,
  getAllBannerConfigs,
  getBannerConfigById,
  getBannerConfigByKey,
  deleteBannerConfig,
  updateBannerConfig,
  getBannerNotifications,
  createBannerNotification,
  getBannerNotificationById,
  updateBannerNotification,
  deleteBannerNotification,
  markAsReadNotification,
};
