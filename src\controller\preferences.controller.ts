import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { MOPreference, preference_type } from "../models/MOPreference";
import { User } from "../models/User";
import { validateModulePermission } from "../helper/common";
import { ROLE_PERMISSIONS } from "../helper/constant";
import { getPagination } from "../helper/utils";

// Create Preference
export const createPreference = async (req: Request, res: Response) => {
    try {
        // Check permission
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'preference',
            ROLE_PERMISSIONS.CREATE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { key, value, type } = req.body;

        // Validate preference type
        if (!Object.values(preference_type).includes(type)) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_PREFERENCE_TYPE"),
                data: { validTypes: Object.values(preference_type) }
            });
        }


        // Check if preference already exists
        const existingPreference = await MOPreference.findOne({
            where: {
                key,
                type,
                organization_id: req.user.organization_id
            }
        });

        if (existingPreference) {
            return res.status(StatusCodes.CONFLICT).json({
                status: false,
                message: res.__("PREFERENCE_ALREADY_EXISTS")
            });
        }

        // Create preference
        const newPreference = await MOPreference.setHeaders(req).create({
            key,
            value: value !== undefined ? value : true,
            type,
            organization_id: req.user.organization_id,
            created_by: req.user.id,
            updated_by: req.user.id
        } as any);

        return res.status(StatusCodes.CREATED).json({
            status: true,
            message: res.__("PREFERENCE_CREATED_SUCCESSFULLY"),
            data: newPreference
        });

    } catch (error) {
        console.error("Error in createPreference:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Preferences
export const getPreferences = async (req: Request, res: Response) => {
    try {
        // Check permission
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'preference',
            ROLE_PERMISSIONS.VIEW,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { page = 1, size = 10, search, type, key } = req.query;
        const { offset } = getPagination(Number(page), Number(size));

        // Build where clause
        const whereClause: any = {
            organization_id: req.user.organization_id
        };

        if (search) {
            whereClause[Op.or] = [
                { key: { [Op.like]: `%${search}%` } }
            ];
        }

        if (type) {
            whereClause.type = type;
        }

        if (key) {
            whereClause.key = key;
        }

        // Get preferences with pagination
        const { count, rows: preferences } = await MOPreference.findAndCountAll({
            where: whereClause,
            order: [['createdAt', 'DESC']],
            limit: Number(size),
            offset: offset,
            attributes: ['id', 'key', 'value', 'type', 'organization_id', 'created_by', 'createdAt', 'updatedAt']
        });

        // Get created_by user details
        const userIds = [...new Set(preferences.map(p => p.created_by).filter(Boolean))];
        const users = await User.findAll({
            where: { id: { [Op.in]: userIds } },
            attributes: ['id', 'user_first_name', 'user_last_name', 'user_full_name', 'user_avatar'],
            raw: true
        });

        const userMap = new Map();
        users.forEach(user => userMap.set(user.id, user));

        // Format response
        const formattedPreferences = preferences.map(preference => ({
            ...preference.toJSON(),
            created_by: preference.created_by ? userMap.get(preference.created_by) : null
        }));

        const totalPages = Math.ceil(count / Number(size));

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PREFERENCES_FETCHED_SUCCESSFULLY"),
            data: formattedPreferences,
            totalItems: count,
            totalPages: totalPages,
            currentPage: Number(page)
        });

    } catch (error) {
        console.error("Error in getPreferences:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Preference by ID
export const getPreferenceById = async (req: Request, res: Response) => {
    try {
        // Check permission
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'preference',
            ROLE_PERMISSIONS.VIEW,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { id } = req.params;

        const preference = await MOPreference.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        if (!preference) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("PREFERENCE_NOT_FOUND")
            });
        }

        // Get created_by user details
        let createdByUser = null;
        if (preference.created_by) {
            createdByUser = await User.findOne({
                where: { id: preference.created_by },
                attributes: ['id', 'user_first_name', 'user_last_name', 'user_full_name', 'user_avatar'],
                raw: true
            });
        }

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PREFERENCE_FETCHED_SUCCESSFULLY"),
            data: {
                ...preference.toJSON(),
                created_by: createdByUser
            }
        });

    } catch (error) {
        console.error("Error in getPreferenceById:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Update Preference
export const updatePreference = async (req: Request, res: Response) => {
    try {
        // Check permission
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'preference',
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { id } = req.params;
        const { key, value, type } = req.body;

        // Find preference
        const preference = await MOPreference.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        if (!preference) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("PREFERENCE_NOT_FOUND")
            });
        }

        // Validate preference type if provided
        if (type && !Object.values(preference_type).includes(type)) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_PREFERENCE_TYPE"),
                data: { validTypes: Object.values(preference_type) }
            });
        }


        // Check for duplicate if key or type is being changed
        if ((key && key !== preference.key) || (type && type !== preference.type)) {
            const existingPreference = await MOPreference.findOne({
                where: {
                    key: key || preference.key,
                    type: type || preference.type,
                    organization_id: req.user.organization_id,
                    id: { [Op.ne]: id }
                }
            });

            if (existingPreference) {
                return res.status(StatusCodes.CONFLICT).json({
                    status: false,
                    message: res.__("PREFERENCE_ALREADY_EXISTS")
                });
            }
        }

        // Build update data
        const updateData: any = {
            updated_by: req.user.id
        };

        if (key !== undefined) updateData.key = key;
        if (value !== undefined) updateData.value = value;
        if (type !== undefined) updateData.type = type;

        // Update preference
        await MOPreference.setHeaders(req).update(updateData, {
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        // Get updated preference
        const updatedPreference = await MOPreference.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PREFERENCE_UPDATED_SUCCESSFULLY"),
            data: updatedPreference
        });

    } catch (error) {
        console.error("Error in updatePreference:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Delete Preference
export const deletePreference = async (req: Request, res: Response) => {
    try {
        // Check permission
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'preference',
            ROLE_PERMISSIONS.DELETE,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { id } = req.params;

        // Find preference
        const preference = await MOPreference.findOne({
            where: {
                id: id,
                organization_id: req.user.organization_id
            }
        });

        if (!preference) {
            return res.status(StatusCodes.NOT_FOUND).json({
                status: false,
                message: res.__("PREFERENCE_NOT_FOUND")
            });
        }

        // Delete preference
        await preference.destroy();

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PREFERENCE_DELETED_SUCCESSFULLY")
        });

    } catch (error) {
        console.error("Error in deletePreference:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Bulk Update Preferences
export const bulkUpdatePreferences = async (req: Request, res: Response) => {
    try {
        // Check permission
        const checkModulePermission = await validateModulePermission(
            req.user,
            req.user.organization_id,
            'preference',
            ROLE_PERMISSIONS.EDIT,
            req?.headers?.["platform-type"]
        );

        if (!checkModulePermission) {
            return res
                .status(StatusCodes.FORBIDDEN)
                .json({ status: false, message: res.__("PERMISSION_DENIED") });
        }

        const { preferences } = req.body;

        if (!Array.isArray(preferences) || preferences.length === 0) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_PREFERENCES_ARRAY")
            });
        }

        let updatedCount = 0;
        let createdCount = 0;
        const results = [];

        for (const pref of preferences) {
            const { key, value, type } = pref;

            // Validate required fields
            if (!key || !type || value === undefined) {
                continue;
            }

            // Find existing preference
            const existingPreference = await MOPreference.findOne({
                where: {
                    key,
                    type,
                    organization_id: req.user.organization_id
                }
            });

            if (existingPreference) {
                // Update existing
                await MOPreference.setHeaders(req).update(
                    { value, updated_by: req.user.id },
                    {
                        where: {
                            id: existingPreference.id,
                            organization_id: req.user.organization_id
                        }
                    }
                );
                updatedCount++;
                results.push({ key, type, action: 'updated', value });
            } else {
                // Create new
                await MOPreference.setHeaders(req).create({
                    key,
                    value,
                    type,
                    organization_id: req.user.organization_id,
                    created_by: req.user.id,
                    updated_by: req.user.id
                } as any);
                createdCount++;
                results.push({ key, type, action: 'created', value });
            }
        }

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PREFERENCES_BULK_UPDATED_SUCCESSFULLY"),
            data: {
                updatedCount,
                createdCount,
                totalProcessed: updatedCount + createdCount,
                results
            }
        });

    } catch (error) {
        console.error("Error in bulkUpdatePreferences:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};

// Get Available Preference Keys and Types
export const getPreferenceOptions = async (req: Request, res: Response) => {
    try {
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("PREFERENCE_OPTIONS_FETCHED_SUCCESSFULLY"),
            data: {
                types: Object.values(preference_type),
                keys: [],
                keyDescriptions: {}
            }
        });

    } catch (error) {
        console.error("Error in getPreferenceOptions:", error);
        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};
