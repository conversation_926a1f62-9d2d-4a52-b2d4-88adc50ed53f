import { Router } from "express";
import authController from "../../controller/auth.controller";
import authValidator from "../../validators/auth.validator";
const router = Router();
import multer from "multer";
import fs from "fs";
import path from "path";

// Define storage for uploaded files
const storage = multer.diskStorage({
  destination: (req: Request, file: any, cb: any) => {
    const avatarPath = path.resolve(
      __dirname,
      "../../uploads/"
    );
    if (!fs.existsSync(avatarPath)) {
      fs.mkdirSync(avatarPath, { recursive: true });
    }
    cb(null, avatarPath);
  },
  filename: (req: any, file: any, cb: any) => {
    cb(null, Date.now() + "-" + file.originalname); // File naming
  },
});

// Initialize Multer with the storage configuration
const upload = multer({ storage: storage });


router.post("/login", authValidator.login(), authController.login);
router.post(
  "/forgot-password",
  authValidator.forgotPassword(),
  authController.forgotPassword,
);
router.post(
  "/forgot-password-verify",
  authValidator.forgotPasswordVerify(),
  authController.forgotPasswordVerify,
);
router.post("/forgot-pin", authValidator.forgotPin(), authController.forgotPin);
router.post("/verify-otp", authValidator.verifyOtp(), authController.verifyOtp);
router.post("/resend-otp", authValidator.resendOtp(), authController.resendOtp);
router.post("/data-entry-dsr", upload.single("excel_data"), authController.dsrDataEntry)
router.post("/data-entry-wsr", upload.single("excel_data"), authController.wsrDataEntry)
router.post("/vat-data-calculation", authController.vatDataCalculation)
router.post("/data-entry-expense", upload.single("excel_data"), authController.expenseDataEntry)

/** Old user data script */
router.post('/old-user-data-script', authController.oldDataUserScript)

router.get('/get-all-username', authController.checkUsernameAvailability)

/** Script for user week day entry data */
router.post('/user-week-day-entry', authController.userWeekDayEntry)

/** Old user change request migration script */
router.post('/old-user-change-request-migration', authController.oldUserChangeRequestMigration)

export default router;
