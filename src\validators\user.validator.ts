import { Segments, Jo<PERSON>, celebrate } from "celebrate";

export default {
  addUser: Joi.object().keys({
    user_first_name: Jo<PERSON>.string().required(),
    user_last_name: <PERSON><PERSON>.string().required(),
    user_middle_name: Jo<PERSON>.string().allow(null, ""),
    user_email: Joi.string().required(),
    address_line1: Joi.string().allow(null, ""),
    address_line2: Joi.string().allow(null, ""),
    country: Joi.string().allow(null, ""),
    pin_code: Joi.string().allow(null, ''),
    user_phone_number: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid mobile number').allow(null),
    emergency_contact: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid home phone number').allow(null),
    user_gender: Joi.string().valid("male", "female", "other").allow(null, ""),
    marital_status: Joi.string().allow(null, ""),
    date_of_birth: Joi.date().allow(null),
    marital_status_other: Joi.string().allow(null, ""),
    user_gender_other: Joi.string().allow(null, ""),
    branch_id: Joi.number().integer().positive(),
    department_id: Joi.number().integer().positive(),
    // role_ids: Joi.array().required().items(Joi.number().required()).min(1),
    role_id: Joi.number().required(),
    joining_date: Joi.date().required(),
    geo_country: Joi.string().allow(null, ""),
    geo_state: Joi.string().allow(null, ""),
    geo_city: Joi.string().allow(null, ""),
    assign_branch_ids: Joi.array().allow(null, ""),
    employment_number: Joi.string().allow(null, "")
  }),
  updateUser: Joi.object().keys({
    user_first_name: Joi.string().required(),
    user_last_name: Joi.string().required(),
    user_middle_name: Joi.string().allow(null, ""),
    user_email: Joi.string().required(),
    address_line1: Joi.string().allow(null, ""),
    address_line2: Joi.string().allow(null, ""),
    country: Joi.string().allow(null, ""),
    pin_code: Joi.string().allow(null, ''),
    user_phone_number: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid phone number').allow(null),
    emergency_contact: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid home mobile number').allow(null),
    user_gender: Joi.string().valid("male", "female", "other").allow(null, ""),
    marital_status: Joi.string().allow(null, ""),
    date_of_birth: Joi.date().allow(null),
    marital_status_other: Joi.string().allow(null, ""),
    user_gender_other: Joi.string().allow(null, ""),
    branch_id: Joi.number().integer().positive(),
    department_id: Joi.number().integer().positive(),
    // role_ids: Joi.array().required().items(Joi.number()).allow(null, ''),
    role_id: Joi.number().required(),
    joining_date: Joi.date().required(),
    user_signature: Joi.string().allow(null, ''),
    user_avatar: Joi.string().allow(null, ''),
    geo_country: Joi.string().allow(null, ""),
    geo_state: Joi.string().allow(null, ""),
    geo_city: Joi.string().allow(null, ""),
    assign_branch_ids: Joi.array().allow(null, ""),
    username: Joi.string(),
    employment_number: Joi.string().allow(null, ""),

  }),
  resetPassword: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        old_password: Joi.string().required(),
        new_password: Joi.string().required(),
      }),
    }),
  setLoginPin: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        is_login_pin: Joi.boolean().required(),
        pin: Joi.when('is_login_pin', {
          is: Joi.boolean().valid(true),
          then: Joi.string().required(),
          otherwise: Joi.string().allow(null, ''),
        }),
      }),
    }),
  switchRole: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        role_id: Joi.number().required(),
      }),
    }),
  resetLoginPin: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        old_pin: Joi.string().required(),
        new_pin: Joi.string().required(),
      }),
    }),
  loginWithPin: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_pin: Joi.string().required(),
        client_ip: Joi.string().allow(null, ""),
      }),
    }),
  updateProfile: Joi.object().keys({
    user_first_name: Joi.string().required(),
    user_last_name: Joi.string().required(),
    user_middle_name: Joi.string().allow(null, ""),
    user_email: Joi.string().required(),
    user_phone_number: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid phone number').allow(null),
    address_line1: Joi.string().required(),
    address_line2: Joi.string().allow(null, ""),
    pin_code: Joi.string().allow(null),
    emergency_contact: Joi.string().pattern(/^[0-9]{10,11}$/).message('Please enter valid home mobile number').allow(null),
    country: Joi.string().required(),
    marital_status_other: Joi.string().allow(null, ""),
    user_gender_other: Joi.string().allow(null, ""),
    user_gender: Joi.string().valid("male", "female", "other").allow(null, ""),
    marital_status: Joi.string().allow(null, ""),
    date_of_birth: Joi.date().required(),
    user_avatar: Joi.allow(),
    user_signature: Joi.string().allow(null, ''),
    geo_country: Joi.string().allow(null, ""),
    geo_state: Joi.string().allow(null, ""),
    geo_city: Joi.string().allow(null, ""),
    username: Joi.string().allow(null, ""),
  }),
  user_avatar: Joi.object(),
  update_notification_token: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        webAppToken: Joi.string().allow(null, ""),
        appToken: Joi.string().allow(null, ""),
      }),
    }),
  send_user_invitation: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_ids: Joi.array().required(),
      }),
    }),
  update_work_schedule: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        user_week_day: Joi.object()
          .keys({
            monday: Joi.string().valid("working", "dayoff").required(),
            tuesday: Joi.string().valid("working", "dayoff").required(),
            wednesday: Joi.string().valid("working", "dayoff").required(),
            thursday: Joi.string().valid("working", "dayoff").required(),
            friday: Joi.string().valid("working", "dayoff").required(),
            saturday: Joi.string().valid("working", "dayoff").required(),
            sunday: Joi.string().valid("working", "dayoff").required()
          })
          .required(),
        user_id: Joi.number().required()
      })
    })
};
