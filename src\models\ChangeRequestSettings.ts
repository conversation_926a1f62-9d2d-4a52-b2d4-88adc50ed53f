"use strict";
import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";
import { addActivity } from "../helper/queue.service";

interface changeRequestSettingsAttributes {
    id: number;
    key: string;
    organization_id: string;
    created_by: number;
    updated_by: number;
}

export class ChangeRequestSettings
    extends Model<changeRequestSettingsAttributes, never>
    implements changeRequestSettingsAttributes {
    id!: number;
    key!: string;
    organization_id!: string;
    created_by!: number;
    updated_by!: number;

    public static setHeaders(headers: any) {
        this.beforeCreate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        this.beforeUpdate(function (data: any) {
            (data as any).headers = headers
            return data
        })

        return this;
    }
}

ChangeRequestSettings.init(
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        key: {
            type: DataTypes.TEXT("long"),
            allowNull: true,
        },
        organization_id: {
            type: DataTypes.STRING,
            allowNull: false,
        },
        created_by: {
            type: DataTypes.INTEGER,
        },
        updated_by: {
            type: DataTypes.INTEGER,
        },
    },
    {
        sequelize: sequelize,
        tableName: "nv_change_request_settings",
        modelName: "ChangeRequestSettings",
    },
);

ChangeRequestSettings.addHook("afterUpdate", async (setting: any) => {
    await addActivity("ChangeRequestSettings", "updated", setting);
});

ChangeRequestSettings.addHook("afterCreate", async (setting: ChangeRequestSettings) => {
    await addActivity("ChangeRequestSettings", "created", setting);
});

ChangeRequestSettings.addHook("beforeBulkUpdate", async (options: any) => {
    options.individualHooks = true;
}); 