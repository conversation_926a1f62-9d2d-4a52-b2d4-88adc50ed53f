/**
 * TypeScript function to update index fields for existing records by organization_id
 * This function will set proper sequential indexes for roles, modules, and widgets
 * within each organization.
 * 
 * Can be used as:
 * 1. API endpoint
 * 2. Standalone function call
 * 3. Command line script
 */

import { sequelize } from '../models';
import { Request, Response } from 'express';

interface UpdateResult {
    success: boolean;
    message: string;
    data?: {
        organizationsProcessed: number;
        rolesUpdated: number;
        modulesUpdated: number;
        widgetsUpdated: number;
        totalRecordsUpdated: number;
    };
    error?: any;
}

interface Organization {
    organization_id: string;
}

interface Record {
    id: number;
}

/**
 * Core function to update indexes by organization
 * @param organizationId - Optional specific organization ID to update
 * @returns Promise<UpdateResult>
 */
export async function updateIndexes(organizationId?: string): Promise<UpdateResult> {
    const transaction = await sequelize.transaction();
    
    try {
        console.log('🚀 Starting index update process...');
        
        // Get all organizations or specific organization
        let organizations: Organization[];
        if (organizationId) {
            organizations = [{ organization_id: organizationId }];
            console.log(`📋 Updating indexes for organization: ${organizationId}`);
        } else {
            // Get all unique organization IDs from roles table
            const orgQuery = `
                SELECT DISTINCT organization_id 
                FROM (
                    SELECT organization_id FROM mo_roles WHERE organization_id IS NOT NULL
                    UNION
                    SELECT organization_id FROM mo_modules WHERE organization_id IS NOT NULL
                    UNION
                    SELECT organization_id FROM mo_widgets WHERE organization_id IS NOT NULL
                ) AS orgs
                ORDER BY organization_id
            `;
            organizations = await sequelize.query(orgQuery, {
                type: sequelize.QueryTypes.SELECT,
                transaction
            }) as Organization[];
            console.log(`📋 Found ${organizations.length} organizations to update`);
        }

        const totalUpdated: any = {
            roles: 0,
            modules: 0,
            widgets: 0
        };

        // Process each organization
        for (const org of organizations) {
            const orgId = org.organization_id;
            console.log(`\n🏢 Processing organization: ${orgId}`);

            // Update Roles
            console.log('  📝 Updating roles...');
            const rolesQuery = `
                SELECT id FROM mo_roles 
                WHERE organization_id = :orgId 
                ORDER BY id ASC
            `;
            const roles = await sequelize.query(rolesQuery, {
                replacements: { orgId },
                type: sequelize.QueryTypes.SELECT,
                transaction
            }) as Record[];

            for (let i = 0; i < roles.length; i++) {
                await sequelize.query(
                    'UPDATE mo_roles SET `index` = :index WHERE id = :id',
                    {
                        replacements: { index: i + 1, id: roles[i].id },
                        type: sequelize.QueryTypes.UPDATE,
                        transaction
                    }
                );
            }
            console.log(`    ✅ Updated ${roles.length} roles`);
            totalUpdated.roles += roles.length;

            // Update Modules
            console.log('  📦 Updating modules...');
            const modulesQuery = `
                SELECT id FROM mo_modules 
                WHERE organization_id = :orgId 
                ORDER BY id ASC
            `;
            const modules = await sequelize.query(modulesQuery, {
                replacements: { orgId },
                type: sequelize.QueryTypes.SELECT,
                transaction
            }) as Record[];

            for (let i = 0; i < modules.length; i++) {
                await sequelize.query(
                    'UPDATE mo_modules SET `index` = :index WHERE id = :id',
                    {
                        replacements: { index: i + 1, id: modules[i].id },
                        type: sequelize.QueryTypes.UPDATE,
                        transaction
                    }
                );
            }
            console.log(`    ✅ Updated ${modules.length} modules`);
            totalUpdated.modules += modules.length;

            // Update Widgets
            console.log('  🎨 Updating widgets...');
            const widgetsQuery = `
                SELECT id FROM mo_widgets 
                WHERE organization_id = :orgId 
                ORDER BY id ASC
            `;
            const widgets = await sequelize.query(widgetsQuery, {
                replacements: { orgId },
                type: sequelize.QueryTypes.SELECT,
                transaction
            }) as Record[];

            for (let i = 0; i < widgets.length; i++) {
                await sequelize.query(
                    'UPDATE mo_widgets SET `index` = :index WHERE id = :id',
                    {
                        replacements: { index: i + 1, id: widgets[i].id },
                        type: sequelize.QueryTypes.UPDATE,
                        transaction
                    }
                );
            }
            console.log(`    ✅ Updated ${widgets.length} widgets`);
            totalUpdated.widgets += widgets.length;
        }

        await transaction.commit();
        
        const totalRecordsUpdated = totalUpdated.roles + totalUpdated.modules + totalUpdated.widgets;
        
        console.log('\n🎉 Index update completed successfully!');
        console.log('📊 Summary:');
        console.log(`   • Organizations processed: ${organizations.length}`);
        console.log(`   • Roles updated: ${totalUpdated.roles}`);
        console.log(`   • Modules updated: ${totalUpdated.modules}`);
        console.log(`   • Widgets updated: ${totalUpdated.widgets}`);
        console.log(`   • Total records updated: ${totalRecordsUpdated}`);

        return {
            success: true,
            message: 'Index update completed successfully',
            data: {
                organizationsProcessed: organizations.length,
                rolesUpdated: totalUpdated.roles,
                modulesUpdated: totalUpdated.modules,
                widgetsUpdated: totalUpdated.widgets,
                totalRecordsUpdated
            }
        };

    } catch (error) {
        await transaction.rollback();
        console.error('❌ Error updating indexes:', error);
        
        return {
            success: false,
            message: 'Failed to update indexes',
            error: error
        };
    }
}

/**
 * API endpoint function to update indexes
 * @param req - Express Request object
 * @param res - Express Response object
 */
export async function updateIndexesAPI(req: Request, res: Response): Promise<void> {
    try {
        const { organization_id } = req.query;
        const organizationId = organization_id as string | undefined;

        // Validate organization_id if provided
        if (organizationId && typeof organizationId !== 'string') {
            res.status(400).json({
                status: false,
                message: 'Invalid organization_id parameter'
            });
            return;
        }

        const result = await updateIndexes(organizationId);

        if (result.success) {
            res.status(200).json({
                status: true,
                message: result.message,
                data: result.data
            });
        } else {
            res.status(500).json({
                status: false,
                message: result.message,
                error: result.error
            });
        }

    } catch (error) {
        console.error('❌ API Error:', error);
        res.status(500).json({
            status: false,
            message: 'Internal server error',
            error: error
        });
    }
}

/**
 * Command line execution function
 */
export async function runCLI(): Promise<void> {
    try {
        // Parse command line arguments
        const args = process.argv.slice(2);
        const orgArg = args.find(arg => arg.startsWith('--org='));
        const organizationId = orgArg ? orgArg.split('=')[1] : undefined;

        const result = await updateIndexes(organizationId);
        
        if (result.success) {
            process.exit(0);
        } else {
            console.error('❌ Script failed:', result.error);
            process.exit(1);
        }
    } catch (error) {
        console.error('❌ CLI Error:', error);
        process.exit(1);
    }
}

// Run CLI if this file is executed directly
// if (require.main === module) {
// setTimeout(() => {
//     runCLI();
// }, 200000)
// }
