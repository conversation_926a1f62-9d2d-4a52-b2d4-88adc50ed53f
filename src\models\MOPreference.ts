import { DataTypes, Model } from "sequelize";
import { sequelize } from ".";
import { addActivity } from "../helper/common";

interface preferenceAttributes {
  id: number;
  key: string;
  value: boolean;
  type: string;
  organization_id: string;
  created_by: number;
  updated_by: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export enum preference_type {
  MAIL = "mail",
  NOTIFICATION = "push",
  BANNER = "banner"
}

export class MOPreference
  extends Model<preferenceAttributes, never>
  implements preferenceAttributes
{
  id!: number;
  key!: string;
  value!: boolean;
  type!: string;
  organization_id!: string;
  created_by!: number;
  updated_by!: number;

  // timestamps!
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  public setHeaders(req: any) {
    this.created_by = req.user?.id;
    this.updated_by = req.user?.id;
    return this;
  }

  public static setHeaders(req: any) {
    return {
      create: (data: any) => {
        return MOPreference.create({
          ...data,
          created_by: req.user?.id,
          updated_by: req.user?.id,
        });
      },
      update: (data: any, options: any) => {
        return MOPreference.update(
          {
            ...data,
            updated_by: req.user?.id,
          },
          options
        );
      },
    };
  }


}

MOPreference.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    key: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100],
      },
    },
    value: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    type: {
      type: DataTypes.ENUM,
      values: Object.values(preference_type),
      allowNull: false,
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
      },
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: "MOPreference",
    tableName: "mo_notification_preferences",
    timestamps: true
  }
);

// Add hooks for activity logging
MOPreference.addHook("afterCreate", async (preference: MOPreference) => {
  try {
    await addActivity("MOPreference", "created", preference);
  } catch (error) {
    console.error("Error logging preference creation activity:", error);
  }
});

MOPreference.addHook("afterUpdate", async (preference: MOPreference) => {
  try {
    await addActivity("MOPreference", "updated", preference);
  } catch (error) {
    console.error("Error logging preference update activity:", error);
  }
});

MOPreference.addHook("afterDestroy", async (preference: MOPreference) => {
  try {
    await addActivity("MOPreference", "deleted", preference);
  } catch (error) {
    console.error("Error logging preference deletion activity:", error);
  }
});

export default MOPreference;
