import { celebrate, Joi, Segments } from "celebrate";
import { preference_type } from "../models/MOPreference";

export default {
    createPreference: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                key: Joi.string().required().messages({
                    'any.required': 'Preference key is required'
                }),
                value: Joi.boolean().required().messages({
                    'any.required': 'Preference value is required',
                    'boolean.base': 'Preference value must be a boolean'
                }),
                type: Joi.string().valid(...Object.values(preference_type)).required().messages({
                    'any.required': 'Preference type is required',
                    'any.only': `Preference type must be one of: ${Object.values(preference_type).join(', ')}`
                })
            }),
        }),

    getPreferences: () =>
        celebrate({
            [Segments.QUERY]: Joi.object().keys({
                page: Joi.number().integer().min(1).optional().messages({
                    'number.base': 'Page must be a number',
                    'number.integer': 'Page must be an integer',
                    'number.min': 'Page must be at least 1'
                }),
                size: Joi.number().integer().min(1).max(100).optional().messages({
                    'number.base': 'Size must be a number',
                    'number.integer': 'Size must be an integer',
                    'number.min': 'Size must be at least 1',
                    'number.max': 'Size cannot exceed 100'
                }),
                search: Joi.string().optional().messages({
                    'string.base': 'Search must be a string'
                }),
                type: Joi.string().valid(...Object.values(preference_type)).optional().messages({
                    'any.only': `Type must be one of: ${Object.values(preference_type).join(', ')}`
                }),
                key: Joi.string().optional()
            }),
        }),

    getPreferenceById: () =>
        celebrate({
            [Segments.PARAMS]: Joi.object().keys({
                id: Joi.number().integer().positive().required().messages({
                    'any.required': 'Preference ID is required',
                    'number.base': 'Preference ID must be a number',
                    'number.integer': 'Preference ID must be an integer',
                    'number.positive': 'Preference ID must be positive'
                })
            }),
        }),

    updatePreference: () =>
        celebrate({
            [Segments.PARAMS]: Joi.object().keys({
                id: Joi.number().integer().positive().required().messages({
                    'any.required': 'Preference ID is required',
                    'number.base': 'Preference ID must be a number',
                    'number.integer': 'Preference ID must be an integer',
                    'number.positive': 'Preference ID must be positive'
                })
            }),
            [Segments.BODY]: Joi.object().keys({
                key: Joi.string().optional(),
                value: Joi.boolean().optional().messages({
                    'boolean.base': 'Preference value must be a boolean'
                }),
                type: Joi.string().valid(...Object.values(preference_type)).optional().messages({
                    'any.only': `Preference type must be one of: ${Object.values(preference_type).join(', ')}`
                })
            }).min(1).messages({
                'object.min': 'At least one field must be provided for update'
            }),
        }),

    deletePreference: () =>
        celebrate({
            [Segments.PARAMS]: Joi.object().keys({
                id: Joi.number().integer().positive().required().messages({
                    'any.required': 'Preference ID is required',
                    'number.base': 'Preference ID must be a number',
                    'number.integer': 'Preference ID must be an integer',
                    'number.positive': 'Preference ID must be positive'
                })
            }),
        }),

    bulkUpdatePreferences: () =>
        celebrate({
            [Segments.BODY]: Joi.object().keys({
                preferences: Joi.array().items(
                    Joi.object().keys({
                        key: Joi.string().required().messages({
                            'any.required': 'Preference key is required'                           
                        }),
                        value: Joi.boolean().required().messages({
                            'any.required': 'Preference value is required',
                            'boolean.base': 'Preference value must be a boolean'
                        }),
                        type: Joi.string().valid(...Object.values(preference_type)).required().messages({
                            'any.required': 'Preference type is required',
                            'any.only': `Preference type must be one of: ${Object.values(preference_type).join(', ')}`
                        })
                    })
                ).min(1).required().messages({
                    'any.required': 'Preferences array is required',
                    'array.base': 'Preferences must be an array',
                    'array.min': 'At least one preference must be provided'
                })
            }),
        }),
};
